server.port=8080
#数据源1
#datasource.no1.driver-class-name:com.mysql.cj.jdbc.Driver
#datasource.no1.url=*************************************************************************************************************
#datasource.no1.username=fras
#datasource.no1.password=czfMHsMaE5FcAfbk
datasource.no1.driver-class-name:com.mysql.cj.jdbc.Driver
datasource.no1.url=********************************************************************************************************************************************************************
datasource.no1.username=root
datasource.no1.password=Rinsys@188
#数据源2(默认没用，俩地址写一样即可)
datasource.no2.driver-class-name:com.mysql.cj.jdbc.Driver
datasource.no2.url=*************************************************************************************************************************************************
datasource.no2.username=root
datasource.no2.password=Rinsys@11
#druid连接池
spring.datasource.type:com.alibaba.druid.pool.DruidDataSource
#最大活跃数
spring.datasource.maxActive:100
#初始化数量
spring.datasource.initialSize:25
#最大连接等待超时时间
spring.datasource.maxWait:60000
#打开PSCache，并且指定每个连接PSCache的大小
spring.datasource.poolPreparedStatements:true
spring.datasource.maxPoolPreparedStatementPerConnectionSize:20
#通过connectionProperties属性来打开mergeSql功能；慢SQL记录
#connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.minIdle:25
spring.datasource.timeBetweenEvictionRunsMillis:60000
spring.datasource.minEvictableIdleTimeMillis:300000
spring.datasource.validationQuery:select 1 from dual
spring.datasource.testWhileIdle:true
spring.datasource.testOnBorrow:false
spring.datasource.testOnReturn:false
#配置监控统计拦截的filters，去掉后监控界面sql将无法统计,'wall'用于防火墙
filters:stat, wall, log4j
#视图配置
spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp
#缓存配置文件位置
spring.cache.ehcache.cofnig=ehcache.xml
#配置这句话,控制台输出sql语句
logging.level.org.fh.mapper=error
#上传文件大小限制
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
#activiti模型检测。、
spring.activiti.check-process-definitions=false
#企业号配置
wechat.cp.corpSecret=sVEWyF1dX-1IhUkGSZrUAI0KaKZ9Fd0EICYk8Ckx9Y-khoVbu3SXVnVE1JDkSjHf
wechat.cp.corpId=wxe648241e577cd822
wechat.cp.appConfigs[0].agentId=1000007
wechat.cp.appConfigs[0].secret=Psz0wJ2H7oLfFUMJhZY07fnvPFs7pgvOj34ic_GrJk0
wechat.cp.appConfigs[0].token=
wechat.cp.appConfigs[0].aesKey=
#上传文件设置
upload.path=/usr/share/fras-upload
#嘉扬上传文件夹
kayang.fileupload.path=/usr/share/fras-upload
#redis配置
spring.redis.host:*************
spring.redis.port:6381
spring.redis.password:
spring.redis.timeout=10000
spring.redis.port.lettuce.pool.max-active=100
spring.redis.port.lettuce.pool.max-wait=10000
spring.redis.port.lettuce.pool.max-idle=20
spring.redis.port.lettuce.pool.min-idle=5
domain.name.url=http://fras.rinsys.com
#设置邮件使用SSL加密
spring.mail.properties.mail.smtp.ssl.enable=true

#kafka配置 - 优化吞吐量版本
spring.kafka.bootstrap-servers=**************:9092

# 生产者配置（用于发送消息到其他主题时）
spring.kafka.producer.acks=1
spring.kafka.producer.retries=3
spring.kafka.producer.batch-size=32768
spring.kafka.producer.buffer-memory=67108864
spring.kafka.producer.linger-ms=10
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# 消费者配置 - 优化高吞吐量
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.fetch-min-size=50000
spring.kafka.consumer.fetch-max-wait=200
spring.kafka.consumer.max-poll-records=100
spring.kafka.consumer.max-poll-interval-ms=600000
spring.kafka.consumer.session-timeout-ms=60000
spring.kafka.consumer.heartbeat-interval-ms=20000

# 监听器配置
spring.kafka.listener.ack-mode=manual_immediate
spring.kafka.listener.concurrency=3
spring.kafka.listener.poll-timeout=3000

# 自定义配置
spring.boot.kafka.group=attendanceConsumerGroup
spring.boot.kafka.topics.att-topic=attTopic