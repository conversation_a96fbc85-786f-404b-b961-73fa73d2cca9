<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.EmployeeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.EmployeeVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="position_num" jdbcType="VARCHAR" property="positionNum"/>
        <result column="actived" jdbcType="VARCHAR" property="actived"/>
        <result column="human_resource" jdbcType="VARCHAR" property="humanResource"/>
        <result column="cost_center" jdbcType="VARCHAR" property="costCenter"/>
        <result column="leader_id" jdbcType="VARCHAR" property="leaderId"/>
        <result column="leaderName" jdbcType="VARCHAR" property="leaderName"/>
        <result column="humanResourceName" jdbcType="VARCHAR" property="humanResourceName"/>
        <result column="functionHeadName" jdbcType="VARCHAR" property="functionHeadName"/>

        <result column="parentUser" jdbcType="VARCHAR" property="parentUser"/>
        <result column="first_level_mgr_id" jdbcType="VARCHAR" property="firstLevelMgrId"/>
        <result column="firstLevelName" jdbcType="VARCHAR" property="firstLevelName"/>
        <result column="secondLevelName" jdbcType="VARCHAR" property="secondLevelName"/>
        <result column="second_level_mgr_id" jdbcType="VARCHAR" property="secondLevelMgrId"/>
        <result column="work_hour_type" jdbcType="VARCHAR" property="workHourType"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="labor_indicator" jdbcType="VARCHAR" property="laborIndicator"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="face_url" jdbcType="VARCHAR" property="faceUrl"/>
        <result column="isImage" jdbcType="VARCHAR" property="isImage"/>
        <result column="rnumber" jdbcType="VARCHAR" property="rnumber"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="function_head" jdbcType="VARCHAR" property="functionHead"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="direct_name" jdbcType="VARCHAR" property="directName"/>
        <result column="face_last_update_time" jdbcType="TIMESTAMP" property="faceLastUpdateTime"/>
        <result column="face_history" jdbcType="INTEGER" property="faceHistory"/>
        <result column="assignee" jdbcType="VARCHAR" property="assignee"/>
        <result column="assigneeName" jdbcType="VARCHAR" property="assigneeName"/>
        <result column="agency" jdbcType="INTEGER" property="agency"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="entry_date" jdbcType="DATE" property="entryDate"/>
        <result column="last_work_date" jdbcType="DATE" property="lastWorkDate"/>
        <result column="leave_date" jdbcType="DATE" property="leaveDate"/>
        <result column="emp_group" jdbcType="VARCHAR" property="empGroup"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, emp_id, name, position, actived, cost_center, first_level_mgr_id,
    second_level_mgr_id, work_hour_type, work_team, production_line, labor_indicator,
    open_id, face_url, face_last_update_time, created_by, created_time, updated_by, updated_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_employee
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_employee
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.EmployeeEntity">
    insert into biz_employee (id, emp_id, name,
       position, actived,
      cost_center, first_level_mgr_id, second_level_mgr_id,
      work_hour_type, work_team, production_line,
      labor_indicator, open_id, face_url,
      face_last_update_time, created_by, created_time,
      updated_by, updated_time)
    values (#{id,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{position,jdbcType=VARCHAR}, #{actived,jdbcType=VARCHAR},
      #{costCenter,jdbcType=VARCHAR}, #{firstLevelMgrId,jdbcType=VARCHAR}, #{secondLevelMgrId,jdbcType=VARCHAR},
      #{workHourType,jdbcType=VARCHAR}, #{workTeam,jdbcType=VARCHAR}, #{productionLine,jdbcType=VARCHAR},
      #{laborIndicator,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, #{faceUrl,jdbcType=VARCHAR},
      #{faceLastUpdateTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP},
      #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.EmployeeEntity">
        insert into biz_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id !=''">
                id,
            </if>
            <if test="empId != null and empId !=''">
                emp_id,
            </if>
            <if test="name != null and name !=''">
                name,
            </if>
            <if test="position != null and position !=''">
                position,
            </if>
            <if test="actived != null and actived !=''">
                actived,
            </if>
            <if test="costCenter != null and costCenter !=''">
                cost_center,
            </if>
            <if test="firstLevelMgrId != null and firstLevelMgrId !=''">
                first_level_mgr_id,
            </if>
            <if test="secondLevelMgrId != null and secondLevelMgrId !=''">
                second_level_mgr_id,
            </if>
            <if test="workHourType != null and workHourType !=''">
                work_hour_type,
            </if>
            <if test="workTeam != null and workTeam !=''">
                work_team,
            </if>
            <if test="productionLine != null and productionLine !=''">
                production_line,
            </if>
            <if test="laborIndicator != null and laborIndicator !=''">
                labor_indicator,
            </if>
            <if test="openId != null and openId !=''">
                open_id,
            </if>
            <if test="faceUrl != null and faceUrl !=''">
                face_url,face_last_update_time,
            </if>
            <if test="createdBy != null and createdBy !=''">
                created_by,
            </if>
            created_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id !=''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="empId != null and empId !=''">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name !=''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="position != null and position !=''">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="actived != null and actived !=''">
                #{actived,jdbcType=VARCHAR},
            </if>
            <if test="costCenter != null and costCenter !=''">
                #{costCenter,jdbcType=VARCHAR},
            </if>
            <if test="firstLevelMgrId != null and firstLevelMgrId !=''">
                #{firstLevelMgrId,jdbcType=VARCHAR},
            </if>
            <if test="secondLevelMgrId != null and secondLevelMgrId !=''">
                #{secondLevelMgrId,jdbcType=VARCHAR},
            </if>
            <if test="workHourType != null and workHourType !=''">
                #{workHourType,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null and workTeam !=''">
                #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null and productionLine !=''">
                #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="laborIndicator != null and laborIndicator !=''">
                #{laborIndicator,jdbcType=VARCHAR},
            </if>
            <if test="openId != null and openId !=''">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="faceUrl != null and faceUrl !=''">
                #{faceUrl,jdbcType=VARCHAR},
                now(),
            </if>
            <if test="createdBy != null and createdBy !=''">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.EmployeeEntity">
        update biz_employee
        <set>
            <if test="empId != null and empId !=''">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name !=''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="position != null and position !=''">
                position = #{position,jdbcType=VARCHAR},
            </if>
            <if test="actived != null and actived !=''">
                actived = #{actived,jdbcType=VARCHAR},
            </if>
            <if test="costCenter != null and costCenter !=''">
                cost_center = #{costCenter,jdbcType=VARCHAR},
            </if>
            <if test="firstLevelMgrId != null and firstLevelMgrId !=''">
                first_level_mgr_id = #{firstLevelMgrId,jdbcType=VARCHAR},
            </if>
            <if test="secondLevelMgrId != null and secondLevelMgrId !=''">
                second_level_mgr_id = #{secondLevelMgrId,jdbcType=VARCHAR},
            </if>
            <if test="workHourType != null and workHourType !=''">
                work_hour_type = #{workHourType,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null and workTeam !=''">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null and productionLine !=''">
                production_line = #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="laborIndicator != null and laborIndicator !=''">
                labor_indicator = #{laborIndicator,jdbcType=VARCHAR},
            </if>
            <if test="openId != null and openId !=''">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="faceUrl != null and faceUrl !=''">
                face_url = #{faceUrl,jdbcType=VARCHAR},
            </if>
            <if test="faceLastUpdateTime != null and faceLastUpdateTime !=''">
                face_last_update_time = now(),
            </if>
            <if test="updatedBy != null and updatedBy !=''">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = now(),
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateEmployeeData" parameterType="org.fh.entity.common.EmployeeEntity">
        update biz_employee
        <set>
            <if test="position != null and position !=''">
                position = #{position,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null and workTeam !=''">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="rnumber != null and rnumber !=''">
                rnumber = #{rnumber,jdbcType=VARCHAR},
            </if>
            <if test="faceUrl != null and faceUrl !=''">
                face_url = #{faceUrl,jdbcType=VARCHAR},
                face_last_update_time = now(),
                face_history = 0,
            </if>
            <if test="updatedBy != null and updatedBy !=''">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            updated_time = now()
        </set>
        where emp_id = #{empId}
    </update>


    <update id="updateEmployeeOpenId" parameterType="org.fh.entity.common.EmployeeEntity">
        update biz_employee
        <set>
            <if test="openId != null and openId !=''">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            updated_time = now()
        </set>
        where emp_id = #{empId}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.EmployeeEntity">
    update biz_employee
    set emp_id = #{empId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      actived = #{actived,jdbcType=VARCHAR},
      cost_center = #{costCenter,jdbcType=VARCHAR},
      first_level_mgr_id = #{firstLevelMgrId,jdbcType=VARCHAR},
      second_level_mgr_id = #{secondLevelMgrId,jdbcType=VARCHAR},
      work_hour_type = #{workHourType,jdbcType=VARCHAR},
      work_team = #{workTeam,jdbcType=VARCHAR},
      production_line = #{productionLine,jdbcType=VARCHAR},
      labor_indicator = #{laborIndicator,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      face_url = #{faceUrl,jdbcType=VARCHAR},
      face_last_update_time = #{faceLastUpdateTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <update id="updateEmployee" parameterType="org.fh.entity.common.EmployeeEntity">
        update biz_employee
        <set>
            <if test="name !=null and name !=''">
                name = #{name},
            </if>
            <if test="actived !=null and actived !=''">
                actived = #{actived},
            </if>
            <if test="workTeam !=null and workTeam !=''">
                work_team = #{workTeam},
            </if>
            <if test="productionLine !=null and productionLine !=''">
                production_line = #{productionLine},
            </if>
            <if test="workHourType !=null and workHourType !=''">
                work_hour_type = #{workHourType},
            </if>
            <if test="position !=null and position !=''">
                `position` = #{position},
            </if>
            <if test="firstLevelMgrId !=null and firstLevelMgrId !=''">
                first_level_mgr_id = #{firstLevelMgrId},
            </if>
            <if test="secondLevelMgrId !=null and secondLevelMgrId !=''">
                second_level_mgr_id = #{secondLevelMgrId},
            </if>
            <if test="laborIndicator !=null and laborIndicator !=''">
                labor_indicator = #{laborIndicator},
            </if>
            <if test="costCenter !=null and costCenter !=''">
                cost_center = #{costCenter},
            </if>
            <if test="updatedBy !=null and updatedBy !=''">
                updated_by = #{updatedBy},
            </if>
            <if test="rnumber != null and rnumber !=''">
                rnumber = #{rnumber,jdbcType=VARCHAR},
            </if>
            updated_time = now()
        </set>
        where emp_id = #{empId};
    </update>

    <!-- 批量修改人员信息 -->
    <update id="updateEmployeeList" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update biz_employee
            <set>
                <if test="item.name !=null and item.name !=''">
                    `name` = #{item.name},
                </if>
                <if test="item.actived !=null and item.actived !=''">
                    actived = #{item.actived},
                </if>
                <if test="item.humanResource !=null and item.humanResource !=''">
                    human_resource = #{item.humanResource},
                </if>
                <if test="item.workTeam !=null and item.workTeam !=''">
                    work_team = #{item.workTeam},
                </if>
                <if test="item.rnumber !=null and item.rnumber !=''">
                    rnumber = #{item.rnumber},
                </if>
                <if test="item.productionLine !=null and item.productionLine !=''">
                    production_line = #{item.productionLine},
                </if>
                <if test="item.workHourType !=null and item.workHourType !=''">
                    work_hour_type = #{item.workHourType},
                </if>
                <if test="item.position !=null and item.position !=''">
                    `position` = #{item.position},
                </if>
                <if test="item.positionNum !=null and item.positionNum !=''">
                    position_num = #{item.positionNum},
                </if>
                <if test="item.empGroup !=null and item.empGroup !=''">
                    emp_group = #{item.empGroup},
                </if>
                <if test="item.firstLevelMgrId !=null">
                    first_level_mgr_id = #{item.firstLevelMgrId},
                </if>
                <if test="item.secondLevelMgrId !=null">
                    second_level_mgr_id = #{item.secondLevelMgrId},
                </if>
                <if test="item.laborIndicator !=null and item.laborIndicator !=''">
                    labor_indicator = #{item.laborIndicator},
                </if>
                <if test="item.costCenter !=null and item.costCenter !=''">
                    cost_center = #{item.costCenter},
                </if>
                <if test="item.entryDate !=null ">
                    entry_date = #{item.entryDate},
                </if>
                <if test="item.lastWorkDate !=null ">
                    last_work_date = #{item.lastWorkDate},
                </if>
                <if test="item.leaveDate !=null ">
                    leave_date = #{item.leaveDate},
                </if>
                <if test="item.eplant !=null and item.eplant !=''">
                    eplant = #{item.eplant},
                </if>
                <if test="item.updatedBy !=null and item.updatedBy !=''">
                    updated_by = #{item.updatedBy},
                </if>
                updated_time = now()
            </set>
            where emp_id = #{item.empId}
        </foreach>
    </update>

    <!-- 分页查询员工信息 -->
    <select id="queryEmployeePage" resultMap="BaseResultMap">
        select e.id,e.emp_id,e.name,e.position,e.actived,e.cost_center,e.first_level_mgr_id,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,e.assignee,e.agency,
        e.open_id,e.face_url,e.face_last_update_time,e.created_by,e.created_time,e.updated_by,e.updated_time,
        cc.department,cc.plant,cc.workshop,cc.vsm,p.direct_name,r.ROLE_NAME as role_name,e.rnumber,
        case
        when e.face_url is null then '未上传'
        when e.face_url = '' then '未上传'
        else '已上传'
        end as isImage,
        e.entry_date ,e.last_work_date,e.leave_date,e.emp_group
        from biz_employee e
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        left join biz_position p on e.position = p.direct
        left join sys_role r on r.RNUMBER = e.rnumber
        <where>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and e.emp_id like '%${empId}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="actived !=null and actived !=''">
                and e.actived = #{actived}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department !=null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="imgStatus !=null and imgStatus ==2">
                and e.face_url is not null
            </if>
            <if test="imgStatus !=null and imgStatus ==1">
                and e.face_url is null
            </if>
        </where>
        order by e.created_time desc
    </select>

    <select id="queryEmployeeToBeDeleteThroughWeWorkPage" resultMap="BaseResultMap">
        select
        e.emp_id,
        e.name,
        e.actived,
        e.work_team,
        e.open_id
        from biz_employee e
        <where>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and e.emp_id like '%${empId}%'
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <choose>
                <when test="empIds!=null and !empIds.isEmpty()">
                    and e.emp_id in
                    <foreach item="item" collection="empIds" open="(" separator="," close=")">'${item}'
                    </foreach>
                </when>
                <otherwise>
                    and e.emp_id = 'A'
                </otherwise>
            </choose>
        </where>
        order by e.created_time desc
    </select>

    <!-- 根据ID查询员工详细信息 -->
    <select id="queryEmployeeDetail" resultMap="BaseResultMap" parameterType="string">
        select e.id,e.emp_id,e.name,e.position,e.position_num,e.actived,e.cost_center,e.first_level_mgr_id,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,
        e.open_id,e.face_url,e.face_last_update_time,e.created_by,e.created_time,e.updated_by,e.updated_time,
        cc.department,cc.plant,cc.workshop,p.direct_name,e.rnumber,r.ROLE_NAME as role_name
        from biz_employee e
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        left join biz_position p on e.position = p.direct
        left join sys_role r on r.RNUMBER = e.rnumber
        where e.id = #{id}
        limit 1
    </select>

    <!-- 根据员工ID查询员工详细信息 -->
    <select id="queryEmployeeDetailByEmpId" resultMap="BaseResultMap" parameterType="string">
        select e.id,e.emp_id,e.name,e.position,e.actived,e.cost_center,e.first_level_mgr_id,e.position_num,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,e.assignee,e.agency,
        e.open_id,e.face_url,e.face_last_update_time,e.created_by,e.created_time,e.updated_by,e.updated_time,
        cc.department,cc.plant,cc.workshop,cc.vsm,p.direct_name,e.rnumber,e.emp_group
        from biz_employee e
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        left join biz_position p on e.position = p.direct
        where e.emp_id = #{empId}
        limit 1
    </select>

    <!-- 根据员工编号获取记录的考勤机 -->
    <select id="queryEmployeeMachine" parameterType="string" resultType="org.fh.vo.common.MachineVo">
        select machine_name as machineName,m.machine_sn as machineSn from biz_employee_machine em
        left join biz_machine m on em.machine_sn = m.machine_sn
        where emp_id = #{empId}
    </select>

    <!-- 查询所有角色信息 -->
    <select id="queryRoleListAll" resultType="map">
        select distinct RNUMBER,ROLE_NAME from sys_role
    </select>

    <!-- 查询所有考勤机 -->
    <select id="queryMachineListAll" resultType="map">
        select distinct machine_sn,machine_name from biz_machine where deleted = '0'
    </select>

    <!-- 根据字典编码查询字典下级名称集合 -->
    <select id="queryDictionariesNames" resultType="string" parameterType="string">
        select NAME from sys_dictionaries where PARENT_ID = (select DICTIONARIES_ID from sys_dictionaries where BIANMA = #{code} limit 1)
    </select>

    <!-- 根据员工编号查询员工信息 -->
    <select id="queryEmployeeByEmpId" parameterType="string" resultMap="BaseResultMap">
        select e.id,e.emp_id,e.name,cc.department,e.position,e.position_num,e.actived,e.cost_center,e.first_level_mgr_id,face_history,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,e.assignee,e.agency,
        e.open_id,e.face_url,e.face_last_update_time,cc.plant,cc.workshop,e.emp_group
        from biz_employee e
        left join biz_cost_center cc on e.cost_center = cc.cost_center
         where emp_id = #{empId} limit 1
    </select>


    <select id="queryEmployeeAndParent" parameterType="string" resultMap="BaseResultMap">
        select e.id,e.emp_id,e.name,cc.department,e.position,e.position_num,e.actived,e.cost_center,e.first_level_mgr_id,e.face_history,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,e.assignee,e.agency,
        ee.second_level_mgr_id as parentUser,cc.function_head,
        e.open_id,e.face_url,e.face_last_update_time,cc.plant,cc.workshop,cc.vsm
        from biz_employee e
        left join biz_employee ee on e.first_level_mgr_id = ee.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
         where e.emp_id = #{empId} limit 1
    </select>

    <!-- 批量修改员工信息离职 -->
    <update id="updateEmployeeListType" parameterType="collection">
        update biz_employee set actived = 'Withdrawn'
        where emp_id in
        <foreach collection="list" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <!-- 批量添加员工信息 -->
    <insert id="addEmployeeList" parameterType="collection">
        insert into
        biz_employee(id,emp_id,name,actived,human_resource,work_team,production_line,work_hour_type,rnumber,eplant,created_by,face_history,agency,created_time)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.empId},#{item.name},#{item.actived},#{item.humanResource},#{item.workTeam},
            #{item.productionLine},#{item.workHourType},#{item.rnumber},#{item.eplant},#{item.createdBy},0,2,now())
        </foreach>
    </insert>

    <!-- 查询所有员工工号 -->
    <select id="queryEmpIdList" resultType="string">
        select emp_id from biz_employee
    </select>

    <!-- 查询所有离职员工工号 -->
    <select id="queryWithdrawnEmpIdList" resultType="string">
        select distinct emp_id from biz_employee where actived = 'Withdrawn'
    </select>

    <select id="queryUserDetailListAll" resultType="map">
        select emp_id as empId,name from biz_employee
    </select>

    <select id="exportEmployeeData" parameterType="org.fh.entity.common.EmployeeEntity" resultType="org.fh.model.exportModel.EmployeeExportModel">
        select e.id,e.emp_id as empId,e.name,e.position,e.actived,e.cost_center,e.first_level_mgr_id as firstLevelMgrId,
        e.second_level_mgr_id as secondLevelMgrId,e.work_hour_type as workHourType,e.work_team as workTeam,e.production_line,e.labor_indicator as
        laborIndicator,
        e.face_url,e.face_last_update_time,
        case
        when e.face_url is null then '未上传'
        when e.face_url = '' then '未上传'
        else '已上传'
        end as isFace,
        cc.department,cc.plant,cc.workshop,p.direct_name as directName,r.ROLE_NAME as roleName
        from biz_employee e
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        left join biz_position p on e.position = p.direct
        left join sys_role r on r.RNUMBER = e.rnumber
        <where>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and e.emp_id like '%${empId}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant like '%${plant}%'
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department !=null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="imgStatus !=null and imgStatus ==2">
                and e.face_url is not null
            </if>
            <if test="imgStatus !=null and imgStatus ==1">
                and e.face_url is null
            </if>
        </where>
        group by e.emp_id
    </select>

    <!-- 根据员工号查询绑定的考勤机 -->
    <select id="queryMachineByEmpId" parameterType="string" resultType="string">
        select machine_sn from biz_employee_machine where emp_id = #{empId}
    </select>

    <!-- 修改员工照片校验状态 -->
    <update id="updateEmployeeFaceType" parameterType="org.fh.entity.common.EmployeeEntity">
        update biz_employee set face_history = #{faceHistory} where emp_id = #{empId}
    </update>

    <select id="queryEmployeeSuperior" resultMap="BaseResultMap">
        	select e.first_level_mgr_id,wg.leader_id,e.name,e.human_resource,cc.department,cc.function_head,
        	cc.plant,cc.workshop,e.second_level_mgr_id,ee.assignee,ee.agency,e.production_line,e.work_team,
            ee.NAME AS firstLevelName,
            eee.NAME AS secondLevelName  from biz_employee e
            LEFT JOIN biz_cost_center cc ON cc.cost_center = e.cost_center
	        LEFT JOIN biz_work_group wg ON e.work_team = wg.name
            left join biz_employee ee on e.first_level_mgr_id = ee.emp_id
            left join biz_employee eee on e.second_level_mgr_id = eee.emp_id
            where e.emp_id = #{empId} and wg.deleted = '0'
    </select>

    <select id="queryEmployeeNameList" parameterType="collection" resultMap="BaseResultMap">
       select e.first_level_mgr_id,wg.leader_id,es.name as leaderName,e.name,e.human_resource,cc.department,cc.function_head,
            e.second_level_mgr_id,ee.assignee,ee.agency,ec.name as functionHeadName,cc.plant,cc.workshop,
            ee.NAME AS firstLevelName,eh.name as humanResourceName,
            eee.NAME AS secondLevelName
            from biz_employee e
            LEFT JOIN biz_cost_center cc ON cc.cost_center = e.cost_center
	        LEFT JOIN biz_work_group wg ON e.work_team = wg.name
	        left join biz_employee es on es.emp_id = wg.leader_id
            left join biz_employee ee on e.first_level_mgr_id = ee.emp_id
            left join biz_employee eee on e.second_level_mgr_id = eee.emp_id
            left join biz_employee eh on eh.emp_id = e.human_resource
            left join biz_employee ec on ec.emp_id = cc.function_head
            where e.emp_id = #{empId} and wg.deleted = '0'
    </select>

    <!-- 查询当前主管所有下级员工 -->
    <select id="queryChildUserAll" parameterType="string" resultMap="BaseResultMap">
        select  e.emp_id,e.name,wg.name as work_team,e.open_id
        from biz_employee e
        left join biz_work_group wg on e.work_team = wg.name
        where wg.leader_id = #{leaderId}
        <if test="empId !=null and empId !=''">
            and (e.emp_id like '%${empId}%' or e.name like '%${empId}%')
        </if>
        and e.actived != 'Withdrawn'
        and wg.deleted = '0'
    </select>

    <select id="queryEmployeeByWorkTeam" parameterType="org.fh.entity.common.EmployeeEntity" resultMap="BaseResultMap">
        select e.emp_id,e.name
        from biz_employee e
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        where e.emp_id like '%${empId}%'
        <if test="plant !=null and plant !=''">
            and cc.plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and cc.workshop = #{workshop}
        </if>
        <if test="workTeam !=null and workTeam !=''">
            and e.work_team = #{workTeam}
        </if>
    </select>

    <!-- 分页查询班组下的员工信息 -->
    <select id="queryEmployeePageByWorkTeam" resultMap="BaseResultMap">
        select e.emp_id,e.name,cc.plant,cc.workshop,e.work_team,e.entry_date,e.last_work_date,e.leave_date
        from biz_employee e
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        <where>
            and e.actived != 'Withdrawn'
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team = #{workTeam}
            </if>
        </where>
    </select>

    <select id="queryEmployeeSchedulePaged" resultMap="BaseResultMap">
      select e.emp_id,e.name,cc.plant,cc.workshop,e.work_team
      from biz_employee e
      left join biz_cost_center cc on cc.cost_center = e.cost_center
      <where>
        and e.actived != 'Withdrawn'
        <if test="workTeam !=null and workTeam !=''">
          and e.work_team = #{workTeam}
        </if>
        <if test="empId !=null and empId !=''">
          and e.emp_id = #{empId}
        </if>
        <if test="name !=null and name !=''">
          and e.name = #{name}
        </if>
      </where>
    </select>



    <!-- 获取所有上传过图片的员工(用于下发考勤机) -->
    <select id="queryEmployeeImageList" resultMap="BaseResultMap">
        select emp_id,`name`,face_url
        from biz_employee where face_url is not null and actived != 'Withdrawn'
    </select>

    <!-- 分页查询上传过图片的员工 -->
    <select id="queryEmployeeImagePage" resultMap="BaseResultMap">
        select emp_id,face_url
        from biz_employee where face_url is not null and actived != 'Withdrawn'
    </select>

    <!-- 根据员工号和日期查询对应的排班班次 -->
    <select id="queryEmployeeWorkType" resultType="string">
        select work_type from biz_schedule s where s.emp_id = #{empId} and s.schedule_date = #{workDate} limit 1
    </select>

    <!-- 查询员工班组对应日期的班次 -->
    <select id="queryEmployeeWorkTeamWorkType" resultType="string">
        select bwt.work_type from biz_employee e
        left join biz_schedule_work_team bwt on bwt.work_team = e.work_team
        where e.emp_id = #{empId} and bwt.schedule_date = #{workDate} limit 1
    </select>

    <!-- 查询员工信息详情 -->
    <select id="checkEmployeeDetail" resultMap="BaseResultMap" parameterType="string">
        select emp_id, name, cc.department,position, actived, e.cost_center, first_level_mgr_id,e.human_resource,cc.plant,cc.workshop,cc.function_head,
        second_level_mgr_id, work_hour_type, work_team, production_line, labor_indicator,direct_name,assignee,agency,
        open_id, face_url, face_last_update_time
        from biz_employee e
        LEFT JOIN biz_cost_center cc ON cc.cost_center = e.cost_center
        left join biz_position p on e.position = p.direct
        where emp_id = #{empId}
        limit 1
    </select>

    <!-- 根据openId查询员工信息 -->
    <select id="queryEmployeeByOpenId" resultMap="BaseResultMap">
        select e.emp_id,e.name,cc.department,e.position,e.actived,e.cost_center,e.first_level_mgr_id,e.human_resource,cc.plant,cc.workshop,cc.function_head,
        e.second_level_mgr_id,e.work_hour_type,e.work_team,e.production_line,e.labor_indicator,p.direct_name,e.assignee,e.agency,ee.name as assigneeName,
        e.open_id,e.face_url,e.face_last_update_time
        from biz_employee e
        LEFT JOIN biz_cost_center cc ON cc.cost_center = e.cost_center
        left join biz_position p on e.position = p.direct
        left join biz_employee ee on e.assignee = ee.emp_id
        where e.open_id = #{openId}
        limit 1
    </select>

    <!-- 修改员工代理人或者是否代理 -->
    <update id="updateEmployeeAssignee">
        update biz_employee
        <set>
            <if test="assignee !=null and assignee !=''">
                assignee = #{assignee},
            </if>
            <if test="agency !=null">
                agency = #{agency}
            </if>
        </set>
        where emp_id = #{empId}
    </update>

    <!-- 查询所有领班 -->
    <select id="queryLeaderAll" resultMap="BaseResultMap">
        select wg.id,wg.name as work_team,e.emp_id,e.name,plant,workshop
        from biz_work_group wg
        left join biz_employee e on e.emp_id = wg.leader_id
        where wg.deleted = '0'
        <if test="plant !=null and plant !=''">
            and wg.plant = #{plant}
        </if>
        <if test="empId !=null and empId !=''">
            and wg.leader_id != #{empId}
        </if>
    </select>

    <!-- 判断该工号是否是员工 -->
    <select id="queryEmployeeIsStaff" parameterType="string" resultType="int">
        select count(*)
        from biz_employee e
        left join biz_position p on e.position = p.direct
        where p.direct_name = '作业员' and e.emp_id = #{empId}
    </select>


    <!-- 查询未绑定的员工 -->
    <select id="synchUserToWechat" resultType="org.fh.entity.common.EmployeeEntity">
        select emp_id as empId, name from biz_employee where open_id is null
    </select>

    <!-- 查询所有厂区和车间信息 -->
    <select id="queryPlantWorkshopAll" resultMap="BaseResultMap">
        SELECT cc.plant,cc.workshop
        FROM biz_employee e
        LEFT JOIN biz_cost_center cc ON e.cost_center = cc.cost_center
        where workshop is not null
        and plant is not null
        <if test="plant !=null and plant !=''">
            and cc.plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and cc.workshop like '%${workshop}%'
        </if>
        <if test="vsm !=null and vsm !=''">
            and cc.vsm like '%${vsm}%'
        </if>
        GROUP BY
        cc.plant,
        cc.workshop
    </select>


    <!-- 查询所有厂区VSM信息 -->
    <select id="queryPlantVSMAll" resultMap="BaseResultMap">
        SELECT cc.plant,cc.vsm as workshop
        FROM biz_employee e
        LEFT JOIN biz_cost_center cc ON e.cost_center = cc.cost_center
        where vsm is not null
        and plant is not null
        <if test="plant !=null and plant !=''">
            and cc.plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and cc.workshop like '%${workshop}%'
        </if>
        <if test="vsm !=null and vsm !=''">
            and cc.vsm like '%${vsm}%'
        </if>
        GROUP BY
        cc.plant,
        cc.vsm
    </select>

    <!-- 查询所有厂区车间人员数量 -->
    <select id="queryPlantWorkshopCountAll" resultType="map">
        SELECT count( emp_id ) as count,cc.plant,cc.workshop,cc.vsm,p.direct_name as position
        FROM biz_employee e
        LEFT JOIN biz_cost_center cc ON e.cost_center = cc.cost_center
        left join biz_position p on p.direct = e.position
        where e.labor_indicator = #{direct} and actived != 'Withdrawn'
        and p.direct_name is not null
        and cc.plant is not null
        GROUP BY
        cc.plant,
        cc.workshop,p.direct_name
    </select>

    <!-- 查询员工直接员工与间接员工数量 -->
    <select id="queryUserTypeCount" resultType="int" parameterType="string">
        select count(*) from biz_employee
        where labor_indicator = #{indicator} and actived != 'Withdrawn'
    </select>

    <select id="queryBorrowUser" resultMap="BaseResultMap" parameterType="string">
        select distinct bu.emp_id,wg.name as work_team,e.open_id
        from biz_borrow_user bu
        left join biz_borrow b on bu.processinstanceid = b.processinstanceid
        left join biz_employee e on e.emp_id = bu.emp_id
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_work_group wg on e.work_team = wg.name
        where b.start_time &lt;= #{borrowDate} and b.end_time &gt;= #{borrowDate}
        and b.apply_user = #{empId}
        and wg.deleted = '0'
    </select>

    <!-- 查询员工信息 -->
    <select id="queryEmployeeInfo" resultType="map">
        select emp_id as empId,name
        from biz_employee
        where emp_id = #{empId} limit 1
    </select>

    <!-- 根据角色编号分页查询员工信息 -->
    <select id="queryEmployeeByRole" parameterType="string" resultMap="BaseResultMap">
        select emp_id as empId,name,emp_id
        from biz_employee
        where rnumber = #{roleNumber}
    </select>

    <!-- 导出角色员工 -->
    <select id="downloadRoleEmployee" parameterType="string" resultType="org.fh.model.exportModel.RoleEmployeeExportModel">
        select emp_id,name
        from biz_employee
        where rnumber = #{roleNumber}
    </select>

    <!-- 查询角色下未离职的员工数量 -->
    <select id="checkRoleEmployeeCount" parameterType="string" resultType="integer">
        select count(*)
        from biz_employee
        where rnumber = #{roleNumber}
        and actived != 'Withdrawn'
    </select>

    <select id="queryActiveEmployee" resultType="org.fh.entity.common.EmployeeEntity">
        select emp_id as empId,first_level_mgr_id as firstLevelMgrId,second_level_mgr_id as secondLevelMgrId
        from biz_employee
        where actived != 'Withdrawn'
    </select>


    <select id="countProcessByAssignee" resultType="int">
        select count(*) from  act_hi_taskinst where PROC_INST_ID_ = #{processInstanceId} and ASSIGNEE_ = #{assignee}  and END_TIME_ is not null
    </select>

    <select id="empIdAndOpenIdMap" parameterType="pd" resultType="java.util.Map">
        select emp_id as empId, open_id as openId
        from biz_employee
        where actived != 'Withdrawn'
        <if test="endDate !=null and endDate !=''">
            and entry_date <![CDATA[<=]]> #{endDate}
        </if>
    </select>
</mapper>