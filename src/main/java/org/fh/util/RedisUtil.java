package org.fh.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.fh.entity.common.AttendanceEntity;
import org.fh.entity.common.EmployeeEntity;
import org.fh.vo.common.EmployeeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Configuration
public class RedisUtil {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    RedisTemplate redisTemplate;

    private static RedisUtil redisUtil;

    @Autowired
    private JedisConnectionFactory jedisConnectionFactory;

    @PostConstruct
    public void init() {
        redisUtil = this;
    }

    /**
     * @Description 根据token查询缓存中的用户信息
     * <AUTHOR>
     * @Date 2019/7/31 13:15
     * @Return SysUser
     * @Param String token token令牌
     * @Throws Exception
     */
    public EmployeeVo querySessionUser(String token) {
//        if (redisUtil.stringRedisTemplate.hasKey("fras_" + token)) {//判断缓存中是否存在
//            EmployeeVo user1 = (EmployeeVo) redisUtil.redisTemplate.opsForValue().get("fras_" + token);
//            return user1;
//        }
//        return null;
        String key = "fras_" + token;
        // 优化：直接获取，减少一次网络请求
        EmployeeVo user = (EmployeeVo) redisUtil.redisTemplate.opsForValue().get(key);
        return user; // 如果不存在会返回null
    }

    /**
     * @Description 判断缓存是否存在
     * <AUTHOR>
     * @Date 2019/11/28 18:16
     * @Return
     * @Param
     * @Throws Exception
     */
    public boolean queryValid(String key) {
        if (redisTemplate.hasKey("fras_" + key)) {
            return true;
        }
        return false;
    }

    /**
     * @Description 根据key删除redis里的缓存数据
     * <AUTHOR>
     * @Date 2019/11/11 9:52
     * @Return
     * @Param
     * @Throws Exception
     */
    public void deleteKey(String key) {
        stringRedisTemplate.delete("fras_" + key);
    }

    /**
     * @Description 根据key删除redis里的缓存数据
     * <AUTHOR>
     * @Date 2019/11/28 15:40
     * @Return
     * @Param String key
     * @Throws Exception
     */
    public void deleteKeyObject(String key) {
        redisTemplate.delete(key);
    }

    /**
     * @Description 获取缓存中的数据
     * <AUTHOR>
     * @Date 2019/11/13 10:30
     * @Return
     * @Param
     * @Throws Exception
     */
    public String getStrValue(String token) {
        if (redisUtil.stringRedisTemplate.hasKey("fras_" + token)) {//判断缓存中是否存在
            String url = redisUtil.stringRedisTemplate.opsForValue().get("fras_" + token);
            return url;
        }
        return null;
    }


    /**
     * @Description 根据key获取值
     * <AUTHOR>
     * @Date 2019/11/28 15:10
     * @Return
     * @Param
     * @Throws Exception
     */
    public Object getDataObject(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * @Description 获取缓存中的存的信息
     * <AUTHOR>
     * @Date 2020/5/7 10:48
     * @Return
     * @Param
     * @Throws Exception
     */
    public Object querySessionInfo(String token) {
        if (redisUtil.stringRedisTemplate.hasKey("fras_" + token)) {//判断缓存中是否存在
           return redisUtil.redisTemplate.opsForValue().get("fras_" + token);
        }
        return null;
    }

    /**
     * @Description 获取前缀数据
     * <AUTHOR>
     * @Date 2019/11/28 13:41
     * @Return
     * @Param String key 前缀
     * @Throws Exception
     */
    public Set<String> getKeyCollection(String key) throws Exception {
        Set<String> dataSet = redisTemplate.keys("fras_" + key + "*");
        return dataSet;
    }

    public void setData(String key, Object o) {
        //用微信小程序openId作为键值放入缓存
        redisTemplate.opsForValue().set("fras_" + key, o);
    }

    //需要进行数据格式转换

    /**
     * @Description 获取缓存中的数据集合
     * <AUTHOR>
     * @Date 2020/1/25 9:43
     * @Return
     * @Param
     * @Throws Exception
     */
    public Set<AttendanceEntity> getKeyList(String key) throws Exception {
        Set<AttendanceEntity> keySet = redisTemplate.keys("fras_" + key + "_*");
        //Set<AttendanceEntity> keySet = stringRedisTemplate.keys("fras_" + key + "_*");
        return keySet;
    }

    /**
     * @Description 获取缓存中的数据set集合键
     * <AUTHOR>
     * @Date 2020/1/25 11:20
     * @Return Set<String>
     * @Param
     * @Throws Exception
     */
    public Set<String> getKeyListStr(String key) throws Exception {
        Set<String> keySet = stringRedisTemplate.keys("fras_" + key + "_*");
        return keySet;
    }


    public Set<String> getKeyListData(String key) throws Exception {
        Set<String> keySet = redisTemplate.keys("fras_" + key + "_*");
        return keySet;
    }

    /**
     * @Description 往缓存中写数据，并且设置失效时间
     * <AUTHOR>
     * @Date 2020/1/20 18:35
     * @Return
     * @Param
     * @Throws Exception
     */
    public void setData(String key, Object o, int number, TimeUnit type) {
        redisTemplate.opsForValue().set("fras_" + key, o, number, type);
    }


    public Object getData(String key, boolean ambiguous) {
        if (ambiguous) {
            return redisTemplate.opsForValue().get("fras_" + key + "*");
        } else {
            return redisTemplate.opsForValue().get("fras_" + key);
        }
    }

    /**
    * @Description 根据工号获取姓名
    * <AUTHOR>
    * @Date 2021/4/12 14:56
    * @Return
    * @Param
    * @Throws Exception
    */
    public String getUserName(String empId){
        if (redisUtil.stringRedisTemplate.hasKey("fras_wechat_" + empId)) {
            EmployeeEntity employeeEntity = (EmployeeEntity)redisTemplate.opsForValue().get("fras_wechat_" + empId);
            if(!ObjectUtils.isNull(employeeEntity)){
                return employeeEntity.getName();
            }
        }
        return "";
    }

    /**
     * @Description 添加缓存班次
     * <AUTHOR>
     * @Date 2019/11/15 13:47
     * @Return
     * @Param
     * @Throws Exception
     */
    public void setStrData(String key, String value) {
        stringRedisTemplate.opsForValue().set("fras_" + key, value);
    }

    /**
     * 设置嘉扬同步数据 type: 1、职员基本信息 2、考勤信息 3、每日考勤分析 4、当月考勤汇总 5、年假结余查看 6、全薪病假查看 7、排班记录 8、补卡记录 9、请假记录
     * 10、加班记录
     *
     * @param date
     * @param type
     * @param value
     */
    public void setKayangSyncResultByDateAndType(String date, String type, String value) {
        stringRedisTemplate.opsForValue().set("fras_kayang_" + date + "_" + type, value);
    }

    @Autowired(required = false)
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        //设置序列化
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(
                Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        // 配置redisTemplate
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        RedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer); // key序列化
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer); // value序列化
        redisTemplate.setHashKeySerializer(stringSerializer); // Hash key序列化
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer); // Hash value序列化
        redisTemplate.afterPropertiesSet();
    }
}
