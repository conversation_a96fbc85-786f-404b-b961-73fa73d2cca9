package org.fh.service.common.impl;

import com.github.pagehelper.PageHelper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisStatusQueryDto;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno1.common.MonthlyAttendanceAnalysisStatusMapper;
import org.fh.service.common.EmployeeService;
import org.fh.service.common.MonthlyAttendanceAnalysisDetailService;
import org.fh.service.common.MonthlyAttendanceAnalysisService;
import org.fh.service.common.MonthlyAttendanceAnalysisStatusService;
import org.fh.service.wx.WxCpMessageService;
import org.fh.util.DateUtil;
import org.fh.util.ResultCode;
import org.fh.util.ResultHelper;
import org.fh.util.StatusConst;
import org.fh.util.UuidUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 说明： 确认状态接口实现类 作者：FH Admin Q313596790 时间：2023-06-13 官网：www.fhadmin.org
 */
@Service
@Transactional //开启事物
@Slf4j
public class MonthlyAttendanceAnalysisStatusServiceImpl implements
    MonthlyAttendanceAnalysisStatusService {

  @Autowired
  private MonthlyAttendanceAnalysisStatusMapper monthlyAttendanceAnalysisstatusMapper;

  @Autowired
  private MonthlyAttendanceAnalysisService monthlyAttendanceAnalysisService;

  @Autowired
  private MonthlyAttendanceAnalysisDetailService monthlyAttendanceAnalysisDetailService;

  @Autowired
  private EmployeeService employeeService;

  @Resource
  private WxCpMessageService wxCpMessageService;

  /**
   * 新增
   *
   * @param pd
   * @throws Exception
   */
  public void save(PageData pd) throws Exception {
    monthlyAttendanceAnalysisstatusMapper.save(pd);
  }

  /**
   * 删除
   *
   * @param pd
   * @throws Exception
   */
  public void delete(PageData pd) throws Exception {
    monthlyAttendanceAnalysisstatusMapper.delete(pd);
  }

  /**
   * 修改
   *
   * @param pd
   * @throws Exception
   */
  public void edit(PageData pd) throws Exception {
    monthlyAttendanceAnalysisstatusMapper.edit(pd);
  }

  /**
   * 列表
   *
   * @param page
   * @throws Exception
   */
  public List<PageData> list(Page page) throws Exception {
    return monthlyAttendanceAnalysisstatusMapper.datalistPage(page);
  }

  /**
   * 列表(全部)
   *
   * @param pd
   * @throws Exception
   */
  public List<PageData> listAll(PageData pd) throws Exception {
    return monthlyAttendanceAnalysisstatusMapper.listAll(pd);
  }

  /**
   * 通过id获取数据
   *
   * @param pd
   * @throws Exception
   */
  public PageData findById(PageData pd) throws Exception {
    return monthlyAttendanceAnalysisstatusMapper.findById(pd);
  }

  /**
   * 批量删除
   *
   * @param ArrayDATA_IDS
   * @throws Exception
   */
  public void deleteAll(String[] ArrayDATA_IDS) throws Exception {
    monthlyAttendanceAnalysisstatusMapper.deleteAll(ArrayDATA_IDS);
  }

  @Override
  public ResultHelper queryPage(MonthlyAttendanceAnalysisStatusQueryDto query) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    // 判断是否是考勤文员
    if(!employeeService.isHr(query.getEmpId())) {
      query.setEmpId(StringUtils.isEmpty(query.getHrId()) ? null : query.getHrId() );
    }
    PageHelper.offsetPage(query.getRows(), query.getOffset());
    com.github.pagehelper.Page<PageData> pageData = monthlyAttendanceAnalysisstatusMapper.queryPage(
        query);
    Map<String, Object> resultMap = new HashMap<String, Object>();
    resultMap.put("data", pageData);
    resultMap.put("count", pageData.getTotal());
    resultMap.put("offset", query.getOffset());
    resultMap.put("row", query.getPage());
    result.setData(resultMap);
    return result;
  }

  @Override
  public ResultHelper initiateAttendanceConfirmation(
      MonthlyAttendanceAnalysisStatusQueryDto query) {
    ResultHelper result = ResultHelper.builder().build();
    try {
      // 更新状态
      monthlyAttendanceAnalysisstatusMapper.initiateAttendanceConfirmation(query);
      // 根据月度考勤数据，生成确认数据
      PageData pd = new PageData();
      pd.put("term", query.getTerm());
      // 获取工号和企业微信号对照关系（在职员工）
      // 根据term，获取财务月结束时间
      pd.put("endDate", DateUtil.addDayToDate(query.getTerm(), 14));
      String term = query.getTerm().substring(0, 7);
      Map<String, String> empMap = employeeService.empIdAndOpenIdMap(pd);
      Map param = new HashMap();
      param.put("term", term);
      if (query.getStatus() == 1) {
        //删除
        monthlyAttendanceAnalysisDetailService.deleteByTerm(pd);

        //新增
        monthlyAttendanceAnalysisService.listMonthlyAttendanceAnalysisEmpIdsByTerm(pd)
            .forEach(empId -> {
              pd.put("MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID", UuidUtil.get32UUID());
              pd.put("TERM", query.getTerm());
              pd.put("EMP_ID", empId);
              pd.put("CONFIRM_STATUS", 0);
              pd.put("SEND_TIME", new Date());
              try {
                monthlyAttendanceAnalysisDetailService.save(pd);

                // 给在职员工发送确认消息（期间2023-01是2022-12-16至2023-01-15.入职日期小于等于期间结束日期，且发起时员工是在职状态的员工）
                if (empMap.get(empId) != null) {
                  wxCpMessageService.sendMsg(empMap.get(empId), param, "",
                      StatusConst.MessageType.MONTHLY_ATTENDANCE_CONFIRM_INIT);
                }

              } catch (Exception e) {
                log.error(e.getMessage());
              }
            });

      }

      result.setMessage(getAttendanceConfirmationStatus(1, query.getStatus()));

    } catch (Exception e) {
      result.setCode(ResultCode.FAILURE);
      result.setMessage(getAttendanceConfirmationStatus(0, query.getStatus()));
      return result;
    }
    return result;
  }

  @Override
  public ResultHelper reSendAttendanceConfirmation(MonthlyAttendanceAnalysisStatusQueryDto query) {
    ResultHelper result = ResultHelper.builder().build();
    try {
      PageData pd = new PageData();
      pd.put("term", query.getTerm());
      pd.put("empId",query.getEmpId());
      Map param = new HashMap();
      param.put("term", query.getTerm());
      Map<String, String> empMap = employeeService.empIdAndOpenIdMap(pd);

      // TODO 更新系统发起时间

      monthlyAttendanceAnalysisService.listMonthlyAttendanceAnalysisNotConfirmedEmpIdsByTerm(pd)
              .forEach(empId -> {
                try {
                  if (empMap.get(empId) != null) {
                    wxCpMessageService.sendMsg(empMap.get(empId), param, "",
                            StatusConst.MessageType.MONTHLY_ATTENDANCE_CONFIRM_INIT);
                  }

                } catch (Exception e) {
                  log.error(e.getMessage());
                }
              });

     }catch (Exception e){
      result.setCode(ResultCode.FAILURE);
      result.setMessage(getAttendanceConfirmationStatus(0, query.getStatus()));
      return result;
    }

    return result;
  }

  private String getAttendanceConfirmationStatus(int code, int status) {
    String result = "";

    if (code == 1) {
      switch (status) {
        case 1:
          result = "发起考勤确认成功！";
          break;
        case 2:
          result = "确认完成成功！";
          break;
        default:
          break;
      }
    } else if (code == 0) {
      switch (status) {
        case 1:
          result = "发起考勤确认失败！";
          break;
        case 2:
          result = "确认完成失败！";
          break;
        default:
          break;
      }
    }

    return result;
  }

  @Override
  public long countByTerm(PageData pd) throws Exception {
    return monthlyAttendanceAnalysisstatusMapper.countByTerm(pd);
  }

  @Override
  public void updateCreateTimeByTerm(PageData pd) throws Exception {
    monthlyAttendanceAnalysisstatusMapper.updateCreateTimeByTerm(pd);
  }

  @Override
  public int getConfirmStatusByTerm(PageData pd) throws Exception {
    return monthlyAttendanceAnalysisstatusMapper.getConfirmStatusByTerm(pd);
  }
}

