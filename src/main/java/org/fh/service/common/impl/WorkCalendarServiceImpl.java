package org.fh.service.common.impl;

import org.apache.commons.beanutils.BeanUtils;
import org.fh.dto.common.workCalendar.WorkCalendarDto;
import org.fh.dto.common.workCalendar.WorkCalendarUpdateDto;
import org.fh.entity.common.WorkCalendarEntity;
import org.fh.mapper.dsno1.common.WorkCalendarMapper;
import org.fh.service.common.WorkCalendarService;
import org.fh.util.*;
import org.fh.vo.common.WorkCalendarVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class WorkCalendarServiceImpl implements WorkCalendarService {

    @Resource
    private WorkCalendarMapper workCalendarMapper;

    /**
     * @Description 初始化本年的周末与休息日
     * <AUTHOR>
     * @Date 2019/11/13 15:33
     * @Return ResultHelper
     * @Param
     * @Throws Exception
     */
    @Override
    @Transactional
    public ResultHelper initWorkCalendar(String username) throws Exception {
        ResultHelper result = ResultHelper.builder().build();
        Calendar c = Calendar.getInstance();
        int year = c.get(Calendar.YEAR);//获取年份，需要与数据库对比
        int count = workCalendarMapper.checkYearForData(String.valueOf(year + 1));//先查询下一年的数据
        if (count > 1) {
            result.setCode(ResultCode.FAILURE);
            result.setStatus(1);
            result.setMessage("已经生成下一年的数据,无需再次生成");
            return result;
        }
        //查询本年的是否已经生成，如果本年的没生成，需要先生成本年的
        int counts = workCalendarMapper.checkYearForData(String.valueOf(year));
        if (counts > 0 && count < 1) {//本年已经生成，生成下一年的
            year ++;
        }
        if(count > 1 && counts > 1){
            result.setCode(ResultCode.FAILURE);
            result.setStatus(1);
            result.setMessage("数据都已经初始化,无需再次生成");
            return result;
        }
        int[] monthArray = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
        Set<String> holidaysSet = new HashSet<>();
        holidaysSet.add(year+"-01-01");
        holidaysSet.add(year+"-04-05");
        holidaysSet.add(year+"-05-01");
        holidaysSet.add(year+"-05-02");
        holidaysSet.add(year+"-05-03");
        holidaysSet.add(year+"-10-01");
        holidaysSet.add(year+"-10-02");
        holidaysSet.add(year+"-10-03");
        holidaysSet.add(year+"-10-04");
        holidaysSet.add(year+"-10-05");
        holidaysSet.add(year+"-10-06");
        holidaysSet.add(year+"-10-07");
        List<String> dateList = new ArrayList<>();//存放一年所有的日期
        for (int i = 0; i < monthArray.length; i++) {
            dateList.addAll(DateUtil.getDayByMonth(year,monthArray[i]));//生成一年所有的日期格式
        }
        List<WorkCalendarEntity> workList = new ArrayList<>();
        for(int i = 0;i<dateList.size();i++){
            /*RestTemplate restTemplate = new RestTemplate();
            //域名加地址
            ResponseEntity<String> responseEntity = restTemplate.postForEntity("http://api.goseek.cn/Tools/holiday?date="+dateList.get(i).replaceAll("-",""),"", String.class);
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String code = String.valueOf(jsonObject.get("data"));
            int type = 1;
            if("3".equals(code)){
                type = 2;
            }else if("1".equals(code)){
                type = 3;
            }*/
            int type = 1;
            if(DateUtil.checkDayOfWeekend(dateList.get(i))){
                type = 2;
            }
            if(holidaysSet.contains(dateList.get(i))){
                type = 3;
            }
            WorkCalendarEntity workCalendarEntity = new WorkCalendarEntity();
            workCalendarEntity.setId(Tools.get32UUID());
            workCalendarEntity.setCreatedBy(username);
            workCalendarEntity.setWorkDate(dateList.get(i));
            workCalendarEntity.setType(type);
            workList.add(workCalendarEntity);
        }
        int size = workList.size();
        for(int i = 0;i<size;i+=50){
            if(i+50 > size){
                workCalendarMapper.addWorkCalendarList(workList.subList(i,size));
            }else{
                workCalendarMapper.addWorkCalendarList(workList.subList(i,i+50));
            }
        }
        return result;
    }

    /**
     * @Description 查询月份的工作日历
     * <AUTHOR>
     * @Date 2019/11/14 9:46
     * @Return ResultHelper
     * @Param String month 日期
     * @Throws Exception
     */
    @Override
    public ResultHelper queryWorkCalendarList(String yearMonth) throws Exception {
        ResultHelper result = ResultHelper.builder().build();
        List<WorkCalendarVo> dataList = workCalendarMapper.queryWorkCalendarList(yearMonth);
        result.setData(dataList);
        return result;
    }

    /**
     * @Description 批量修改日历数据
     * <AUTHOR>
     * @Date 2019/11/14 10:35
     * @Return ResultHelper
     * @Param WorkCalendarUpdateDto workCalendarUpdateDto
     * @Throws Exception
     */
    @Override
    public ResultHelper updateWorkCalendarList(WorkCalendarUpdateDto workCalendarUpdateDto,String updateBy) throws Exception {
        ResultHelper result = ResultHelper.builder().build();
        List<WorkCalendarDto> paramList = workCalendarUpdateDto.getDataList();
        int status = 1;
        if(!ObjectUtils.isNull(paramList)){
            List<WorkCalendarEntity> workCalendarEntities = new ArrayList<>();
            for(int i=0;i<paramList.size();i++){
                WorkCalendarEntity work = new WorkCalendarEntity();
                BeanUtils.copyProperties(work,paramList.get(i));
                work.setUpdateBy(updateBy);
                workCalendarEntities.add(work);
                //workCalendarMapper.updateWorkCalendarData(work);
            }
            status = workCalendarMapper.updateWorkCalendarList(workCalendarEntities);
        }
        if(status < 1){
            result.setStatus(1);
            result.setMessage("工作日历修改失败");
            result.setCode(ResultCode.FAILURE);
            return result;
        }
        result.setMessage("工作日历修改成功");
        return result;
    }

    /**
    * @Description 根据开始日期和结束日期查询中间的休息日期
    * <AUTHOR>
    * @Date 2021/4/9 11:12
    * @Return List<String>
    * @Param
    * @Throws Exception
    */
    @Override
    public List<String> queryHolidayList(String startDate, String endDate) throws Exception {
        List<String> dateList = workCalendarMapper.queryHolidayList(startDate,endDate);
        if(ObjectUtils.isNull(dateList)){
            dateList = new ArrayList<>();
        }
        return dateList;
    }

    /**
    * @Description 根据开始日期和结束日期查询中间的国假日期（type=3）
    * <AUTHOR>
    * @Date 2024/12/19
    * @Return List<String>
    * @Param
    * @Throws Exception
    */
    @Override
    public List<String> queryNationalHolidayList(String startDate, String endDate) throws Exception {
        List<String> dateList = workCalendarMapper.queryNationalHolidayList(startDate,endDate);
        if(ObjectUtils.isNull(dateList)){
            dateList = new ArrayList<>();
        }
        return dateList;
    }
}
