package org.fh.service.common.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.fh.dto.common.attendance.AttendanceQueryDto;
import org.fh.dto.common.dailyAttendanceAnalysis.DailyAttendanceAnalysisQueryDto;
import org.fh.dto.common.email.ReportEmailAddDto;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno1.common.DailyAttendanceAnalysisMapper;
import org.fh.mapper.dsno1.common.ReplenishAttendanceMapper;
import org.fh.mapper.dsno1.common.ReportEmailMapper;
import org.fh.model.exportModel.ContinuousWorkEmployeeExportModel;
import org.fh.service.common.DailyAttendanceAnalysisService;
import org.fh.service.common.EmployeeService;
import org.fh.service.wx.WxCpMessageService;
import org.fh.util.DateUtil;
import org.fh.util.RedisUtil;
import org.fh.util.ResultCode;
import org.fh.util.ResultHelper;
import org.fh.util.UuidUtil;
import org.fh.util.EasyExcelUtil;
import org.fh.util.mail.SimpleMailSender;
import org.fh.util.Const;
import org.fh.vo.common.AbnormalAttendanceVo;
import org.fh.vo.common.DailyAttendanceAnalysisVo;
import org.fh.vo.common.EmpMonthlyAttendanceStatusVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;

/**
 * 说明： 每日考勤分析接口实现类 作者：FH Admin Q313596790 时间：2023-06-02 官网：www.fhadmin.org
 */
@Slf4j
@Service
@Transactional //开启事物
public class DailyAttendanceAnalysisServiceImpl implements DailyAttendanceAnalysisService {

  @Autowired
  private DailyAttendanceAnalysisMapper dailyAttendanceAnalysisMapper;

  @Autowired
  private ReplenishAttendanceMapper replenishAttendanceMapper;

  @Resource
  private RedisUtil redisUtil;

  @Autowired
  private EmployeeService employeeService;

  @Resource
  private WxCpMessageService wxCpMessageService;

  @Autowired
  private ReportEmailMapper reportEmailMapper;

  @Value("${upload.path}")
  private String uploadPath;

  /**
   * 新增
   *
   * @param pd
   * @throws Exception
   */
  public void save(PageData pd) throws Exception {
    dailyAttendanceAnalysisMapper.save(pd);
  }

  /**
   * 删除
   *
   * @param pd
   * @throws Exception
   */
  public void delete(PageData pd) throws Exception {
    dailyAttendanceAnalysisMapper.delete(pd);
  }

  /**
   * 修改
   *
   * @param pd
   * @throws Exception
   */
  public void edit(PageData pd) throws Exception {
    dailyAttendanceAnalysisMapper.edit(pd);
  }

  /**
   * 列表
   *
   * @param page
   * @throws Exception
   */
  public List<PageData> list(Page page) throws Exception {
    return dailyAttendanceAnalysisMapper.datalistPage(page);
  }

  /**
   * 列表(全部)
   *
   * @param pd
   * @throws Exception
   */
  public List<PageData> listAll(PageData pd) throws Exception {
    return dailyAttendanceAnalysisMapper.listAll(pd);
  }

  /**
   * 通过id获取数据
   *
   * @param pd
   * @throws Exception
   */
  public PageData findById(PageData pd) throws Exception {
    return dailyAttendanceAnalysisMapper.findById(pd);
  }

  /**
   * 批量删除
   *
   * @param ArrayDATA_IDS
   * @throws Exception
   */
  public void deleteAll(String[] ArrayDATA_IDS) throws Exception {
    dailyAttendanceAnalysisMapper.deleteAll(ArrayDATA_IDS);
  }

  @Override
  public ResultHelper queryAttendanceMonthByKayang(AttendanceQueryDto queryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();

    List<DailyAttendanceAnalysisVo> dailyAttendanceAnalysisVoList = dailyAttendanceAnalysisMapper.queryAttendanceMonthByKayang(
        queryDto);

    EmpMonthlyAttendanceStatusVo empMonthlyAttendanceStatusVo = null;

    List<EmpMonthlyAttendanceStatusVo> empMonthlyAttendanceStatusVoList = new ArrayList<>();
    for (DailyAttendanceAnalysisVo item : dailyAttendanceAnalysisVoList) {
      empMonthlyAttendanceStatusVo = new EmpMonthlyAttendanceStatusVo();
      empMonthlyAttendanceStatusVo.setAttendanceDate(item.getTerm());
      empMonthlyAttendanceStatusVo.setAttendanceStatus(item.getWorkStatus());
      empMonthlyAttendanceStatusVoList.add(empMonthlyAttendanceStatusVo);
    }
    result.setData(empMonthlyAttendanceStatusVoList);
    return result;

  }


  @Override
  public ResultHelper queryAttendanceByKayang(AttendanceQueryDto queryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();

    List<DailyAttendanceAnalysisVo> dailyAttendanceAnalysisVoList = dailyAttendanceAnalysisMapper.queryAttendanceByKayang(
        queryDto);

    if (dailyAttendanceAnalysisVoList != null && dailyAttendanceAnalysisVoList.size() > 0) {
      DailyAttendanceAnalysisVo dailyAttendanceAnalysisVo = dailyAttendanceAnalysisVoList.get(0);

      result.setData(dailyAttendanceAnalysisVo.getWorkDesc());
    } else {
      result.setData(null);
    }

    return result;
  }

  @Override
  public ResultHelper uploadDailyAttendanceAnalysis(List<DailyAttendanceAnalysisVo> list)
      throws Exception {
    ResultHelper result = ResultHelper.builder().build();

    if (list == null || list.size() == 0) {
      result.setMessage("没读取到记录！");
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      return result;
    }

    // 记录同步数量
    redisUtil.setKayangSyncResultByDateAndType(DateUtil.getDay(), "3", String.valueOf(list.size()));

    try {
      PageData pd = new PageData();
      for (DailyAttendanceAnalysisVo item : list) {

        pd.clear();
        String term = item.getTerm().replaceAll("\\/", "-");
        item.setTerm(term);
        pd.put("term", term);
        pd.put("empId", item.getEmpId());
        DailyAttendanceAnalysisVo exists = dailyAttendanceAnalysisMapper.findByTermAndEmpId(
            pd);

        if (exists == null) {
            item.setDailyAttendanceAnalysisId(UuidUtil.get32UUID());
            if (StringUtils.isEmpty(item.getBeginTime())) {
              item.setBeginTime(null);
            }
            if (StringUtils.isEmpty(item.getEndTime())) {
              item.setEndTime(null);
            }
            dailyAttendanceAnalysisMapper.upload(item);
        } else {
            if (StringUtils.isEmpty(item.getBeginTime())) {
              item.setBeginTime(null);
            }
            if (StringUtils.isEmpty(item.getEndTime())) {
              item.setEndTime(null);
            }
            dailyAttendanceAnalysisMapper.update(item);
        }
      }
    } catch (Exception e) {
      log.debug(e.getMessage());
      result.setMessage("上传每日考勤失败");
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
    }

    return result;
  }

  @Override
  public void sendMsgToAbnormalAttendances() throws Exception {
    // 获取当前考勤月的日期范围
    List<String> attendanceMonthRange = DateUtil.getCurrentAttendanceMonthRange();
    String startDate = attendanceMonthRange.get(0);
    String endDate = attendanceMonthRange.get(1);
    
    PageData param = new PageData();
    param.put("startDate", startDate);
    param.put("endDate", endDate);
    List<AbnormalAttendanceVo> data = dailyAttendanceAnalysisMapper.listAbnormalAttendancesByDateRange(param);
    Map<String, String> empMap = employeeService.empIdAndOpenIdMap(new PageData());
    StringBuffer msg = null;
    for (AbnormalAttendanceVo item : data) {
      if (empMap.get(item.getEmpId()) != null) {
        msg = new StringBuffer();

        msg.append(item.getName()).append(" 在").append(item.getTerm()).append(" 考勤异常:")
            .append(item.getMsg())
            .append("\r\n请进入考勤日历查看详情，如不处理，将影响本月薪资。");

        wxCpMessageService.sendMsg(empMap.get(item.getEmpId()), msg.toString());
      }
    }

  }
  


  @Override
  public void sendAbnomalAttendanceNoticeMsg() throws Exception {
    PageData param = new PageData();
    param.put("startDate", DateUtil.getRecentTwoFinancialMonthsStart());
    param.put("endDate", DateUtil.getOffDayDate("-3"));

    List<PageData> data = dailyAttendanceAnalysisMapper.listAbnormalAttendancesThreeDayByLeader(new PageData());
    Map<String, String> empMap = employeeService.empIdAndOpenIdMap(new PageData());
    StringBuffer msg = null;
    for (PageData item : data) {
      if (empMap.get(item.get("first_level_mgr_id")) != null) {
        msg = new StringBuffer();
        msg.append("您有员工超过3天未处理最近两个连续考勤月的异常考勤数据，员工姓名如下:\r\n");
        msg.append(item.getString("employees"));

        wxCpMessageService.sendMsg(empMap.get(item.get("first_level_mgr_id")), msg.toString());
      }
    }

    param = new PageData();
    param.put("startDate", DateUtil.getRecentTwoFinancialMonthsStart());
    param.put("endDate", DateUtil.getOffDayDate("-7"));
    data = dailyAttendanceAnalysisMapper.listAbnormalAttendancesSevenDayByManager(new PageData());

    msg = null;
    for (PageData item : data) {
      if (empMap.get(item.get("second_level_mgr_id")) != null) {
        msg = new StringBuffer();
        msg.append("您有员工超过7天未处理最近两个连续考勤月的异常考勤数据，员工姓名如下:\r\n");
        msg.append(item.getString("employees"));

        wxCpMessageService.sendMsg(empMap.get(item.get("second_level_mgr_id")), msg.toString());
      }
    }
  }

  @Override
  public List<String> getEmployeesWorkedSixConsecutiveDays(PageData pd) throws Exception {
    return dailyAttendanceAnalysisMapper.getEmployeesWorkedSixConsecutiveDays(pd);
  }

  /**
   * 分页查询每日考勤分析数据
   *
   * @param queryDto 查询条件DTO
   * @return 分页查询结果
   * @throws Exception
   */
  @Override
  public ResultHelper queryDailyAttendanceAnalysisPage(DailyAttendanceAnalysisQueryDto queryDto) throws Exception {
    ResultHelper result = ResultHelper.builder().build();
    
    try {
      // 创建 PageData 对象用于传递查询条件
      PageData pd = new PageData();
      
      // 设置查询条件
      if (queryDto.getEmpId() != null && !queryDto.getEmpId().trim().isEmpty()) {
        pd.put("EMP_ID", queryDto.getEmpId().trim());
      }
      
      if (queryDto.getName() != null && !queryDto.getName().trim().isEmpty()) {
        pd.put("NAME", queryDto.getName().trim());
      }
      
      if (queryDto.getTerm() != null && !queryDto.getTerm().trim().isEmpty()) {
        pd.put("TERM", queryDto.getTerm().trim());
      }
      
      if (queryDto.getEplant() != null && !queryDto.getEplant().trim().isEmpty()) {
        pd.put("eplant", queryDto.getEplant().trim());
      }
      
      if (queryDto.getWorkTeam() != null && !queryDto.getWorkTeam().trim().isEmpty()) {
        pd.put("WORK_TEAM", queryDto.getWorkTeam().trim());
      }

      // 设置财务月开始和结束时间
      if(!StringUtils.isEmpty(queryDto.getMonth())) {
        pd.put("startDate", DateUtil.getFinanceDate(queryDto.getMonth()).get(0));
        pd.put("endDate", DateUtil.getFinanceDate(queryDto.getMonth()).get(1));
      } else {
        pd.put("startDate", DateUtil.getRecentFinancialMonthsStart());
        pd.put("endDate", DateUtil.getRecentFinancialMonthsEnd());
      }
      
      // 创建 Page 对象用于分页
      Page page = new Page();
      page.setCurrentPage(queryDto.getPage());
      page.setShowCount(queryDto.getOffset());
      page.setPd(pd);
      
      // 执行分页查询
      List<PageData> dataList = dailyAttendanceAnalysisMapper.datalistPage(page);


      // 为每条记录计算异常类型
      for (PageData row : dataList) {
        String shiftVal = row.getString("SHIFT");
        String beginTime = toDateTimeString(row.get("BEGIN_TIME"));
        String endTime = toDateTimeString(row.get("END_TIME"));

        BigDecimal toDecimal0 = new BigDecimal("0");
        BigDecimal uahr = row.get("UAHR") == null ? toDecimal0 : new BigDecimal(row.get("UAHR").toString());
        BigDecimal rewk = row.get("REWK") == null ? toDecimal0 : new BigDecimal(row.get("REWK").toString());
        BigDecimal limn = row.get("LIMN") == null ? toDecimal0 : new BigDecimal(row.get("LIMN").toString());
        BigDecimal cder = row.get("CDER") == null ? toDecimal0 : new BigDecimal(row.get("CDER").toString());
        BigDecimal wkhr = row.get("WKHR") == null ? toDecimal0 : new BigDecimal(row.get("WKHR").toString());

        java.util.LinkedHashSet<String> types = new java.util.LinkedHashSet<>();

        // 请假问题：工作班次且旷工时数>0
        if (isWorkingShift(shiftVal) && uahr.compareTo(toDecimal0) > 0) {
          types.add("请假问题");
        }

        // 加班问题：休息班次且超时工作>0
        if (isRestShift(shiftVal) && rewk.compareTo(toDecimal0) > 0) {
          types.add("加班问题");
        }

        // 迟到
        if (limn.compareTo(toDecimal0) > 0) {
          types.add("迟到");
        }

        // 奇次卡
        if (cder.compareTo(toDecimal0) > 0) {
          types.add("奇次卡");
        }

        // 超时工作
        if (rewk.compareTo(toDecimal0) > 0) {
          types.add("超时工作");
        }

        // 排班问题：
        boolean hasSwipe = org.apache.commons.lang3.StringUtils.isNotBlank(beginTime) || org.apache.commons.lang3.StringUtils.isNotBlank(endTime);
        if ((isRestShift(shiftVal) && hasSwipe && rewk.compareTo(toDecimal0) == 0)
            || (isWorkingShift(shiftVal) && !hasSwipe)) {
          types.add("排班问题");
        }

        // 规则：当同时出现"加班问题"和"超时工作"时，仅保留"加班问题"
        if (types.contains("加班问题") && types.contains("超时工作")) {
          types.remove("超时工作");
        }

        // 新增规则：当同时出现"请假问题、奇次卡"时
        // 当排班为A班、夜班、B1班或者C1班，旷工时数显示大于0，且没有打卡记录的情况下，考勤异常类型为请假问题
        boolean hasSwipeRecord = org.apache.commons.lang3.StringUtils.isNotBlank(beginTime) || org.apache.commons.lang3.StringUtils.isNotBlank(endTime);
        if (types.contains("请假问题") && types.contains("排班问题")) {
          if (isWorkingShift(shiftVal) && uahr.compareTo(toDecimal0) > 0 && !hasSwipeRecord) {
            types.remove("排班问题");
          }
        }

        // 新增规则：当同时出现"奇次卡、排班问题"时
        // 当排班为A班、夜班、B1班、C1班或休息，奇次卡次数大于0时考勤异常类型为奇次卡
        if (types.contains("请假问题") && types.contains("奇次卡")) {
          if ((isWorkingShift(shiftVal) || isRestShift(shiftVal)) && cder.compareTo(toDecimal0) > 0) {
            types.remove("请假问题");
          }
        }

        // 组装异常类型
        if (types.isEmpty()) {
          row.put("EXCEPTION_TYPE", "");
        } else {
          row.put("EXCEPTION_TYPE", String.join("、", types));
        }
      }
      
      // 构建返回结果
      Map<String, Object> resultMap = new HashMap<>();
      resultMap.put("varList", dataList);
      resultMap.put("page", page);
      resultMap.put("pd", pd);
      resultMap.put("count", page.getTotalResult());
      resultMap.put("offset", queryDto.getOffset());
      resultMap.put("row", queryDto.getPage());
      
      result.setData(resultMap);
      result.setStatus(0);
      result.setMessage("查询成功");
      
    } catch (Exception e) {
      log.error("分页查询每日考勤分析数据异常", e);
      result.setStatus(1);
      result.setMessage("查询失败：" + e.getMessage());
    }
    
    return result;
  }

  private boolean isWorkingShift(String shiftVal) {
    if (shiftVal == null) {
      return false;
    }
    return "A班".equals(shiftVal) || "夜班".equals(shiftVal) || "C1班".equals(shiftVal) || "B1班".equals(shiftVal);
  }

  private boolean isRestShift(String shiftVal) {
    if (shiftVal == null) {
      return false;
    }
    return shiftVal.contains("休");
  }

  /**
   * 将对象安全转换为日期时间字符串。
   * 支持 java.util.Date 及其子类（如 java.sql.Timestamp）；
   * 其他类型调用 toString，null 返回 null。
   */
  private String toDateTimeString(Object value) {
    if (value == null) {
      return null;
    }
    if (value instanceof java.util.Date) {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      return sdf.format((java.util.Date) value);
    }
    return value.toString();
  }

  @Override
  public void remindContinuousWork() throws Exception {
    try {
      log.info("开始执行连续工作提醒任务");
      
      // 计算查询开始日期（当前时间往前推6天）
      String startDate = DateUtil.getOffDayDate("-6");
      
      PageData param = new PageData();
      param.put("startDate", startDate);
      
      // 获取连续工作员工详细信息
      List<ContinuousWorkEmployeeExportModel> continuousWorkEmployees = 
          dailyAttendanceAnalysisMapper.getContinuousWorkEmployeeDetails(param);
      
      if (continuousWorkEmployees == null || continuousWorkEmployees.isEmpty()) {
        log.info("没有发现连续工作>=6天的员工");
        return;
      }
      
      log.info("发现{}名员工连续工作>=6天", continuousWorkEmployees.size());
      
      // 按车间分组
      Map<String, List<ContinuousWorkEmployeeExportModel>> workshopEmployeeMap = new HashMap<>();
      for (ContinuousWorkEmployeeExportModel employee : continuousWorkEmployees) {
        String key = employee.getPlant() + "_" + employee.getWorkshop();
        workshopEmployeeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(employee);
      }
      
      // 按车间发送邮件
      for (Map.Entry<String, List<ContinuousWorkEmployeeExportModel>> entry : workshopEmployeeMap.entrySet()) {
        String[] plantWorkshop = entry.getKey().split("_");
        String plant = plantWorkshop[0];
        String workshop = plantWorkshop.length > 1 ? plantWorkshop[1] : "";
        List<ContinuousWorkEmployeeExportModel> workshopEmployees = entry.getValue();
        
        // 查询该车间的邮件接收人
        ReportEmailAddDto emailInfo = reportEmailMapper.findByPlantAndWorkshop(plant, workshop);
        
        if (emailInfo == null) {
          log.warn("未找到厂区[{}]车间[{}]的邮件接收人配置，跳过发送", plant, workshop);
          continue;
        }
        
        // 生成Excel附件
        String fileName = generateExcelFile(workshopEmployees, plant, workshop);
        
        // 构建邮件内容
        String subject = String.format("连续工作提醒 - %s %s", plant, workshop);
        StringBuilder content = new StringBuilder();
        content.append("<html><body>");
        content.append("<h3>连续工作提醒</h3>");
        content.append("<p>以下员工连续工作天数>=6天，请关注：</p>");
        content.append("<table border='1' cellpadding='5' cellspacing='0'>");
        content.append("<tr><th>厂区</th><th>车间</th><th>姓名</th><th>工号</th><th>班组</th><th>连续工作天数</th></tr>");
        
        for (ContinuousWorkEmployeeExportModel emp : workshopEmployees) {
          content.append("<tr>");
          content.append("<td>").append(emp.getPlant() != null ? emp.getPlant() : "").append("</td>");
          content.append("<td>").append(emp.getWorkshop() != null ? emp.getWorkshop() : "").append("</td>");
          content.append("<td>").append(emp.getName() != null ? emp.getName() : "").append("</td>");
          content.append("<td>").append(emp.getEmpId() != null ? emp.getEmpId() : "").append("</td>");
          content.append("<td>").append(emp.getWorkTeam() != null ? emp.getWorkTeam() : "").append("</td>");
          content.append("<td>").append(emp.getContinuousDays() != null ? emp.getContinuousDays() : 0).append("</td>");
          content.append("</tr>");
        }
        
        content.append("</table>");
        content.append("<p>详细信息请查看附件。</p>");
        content.append("</body></html>");
        
        // 构建收件人和抄送人邮箱地址
        List<String> toEmails = new ArrayList<>();
        if (StringUtils.isNotBlank(emailInfo.getLeaderEmail())) {
          toEmails.add(emailInfo.getLeaderEmail().trim());
        }
        if (StringUtils.isNotBlank(emailInfo.getManagerEmail())) {
          toEmails.add(emailInfo.getManagerEmail().trim());
        }
        
        // 对收件人去重
        toEmails = toEmails.stream().distinct().collect(java.util.stream.Collectors.toList());
        
        String toEmailStr = String.join(",", toEmails);
        String ccEmailStr = "";
        
        // 处理抄送人
        if (StringUtils.isNotBlank(emailInfo.getDirectorEmail())) {
          ccEmailStr = emailInfo.getDirectorEmail().trim();
        }
        
        // 发送邮件（TO: 主管和经理，CC: 厂长）
        if (!toEmails.isEmpty()) {
          try {
            SimpleMailSender.sendEmailWithCC(
                Const.SMTP, Const.PORT, Const.EMAIL, Const.PASSWORD,
                toEmailStr, ccEmailStr, subject, content.toString(), "2", fileName
            );
            log.info("已向车间[{} {}]发送连续工作提醒邮件，收件人：{}，抄送：{}", 
                     plant, workshop, toEmailStr, ccEmailStr);
          } catch (Exception e) {
            log.error("向车间[{} {}]发送连续工作提醒邮件失败: {}", plant, workshop, e.getMessage());
          }
        } else {
          log.warn("车间[{} {}]没有配置主管和经理邮箱，跳过发送", plant, workshop);
        }
        
        // 清理临时文件
        try {
          File tempFile = new File(fileName);
          if (tempFile.exists()) {
            tempFile.delete();
          }
        } catch (Exception e) {
          log.warn("删除临时文件失败: {}", e.getMessage());
        }
      }
      
      log.info("连续工作提醒任务执行完成");
      
    } catch (Exception e) {
      log.error("执行连续工作提醒任务时发生异常: {}", e.getMessage(), e);
      throw e;
    }
  }
  
  /**
   * 生成Excel文件
   * @param employees 员工列表
   * @param plant 厂区
   * @param workshop 车间
   * @return 文件路径
   * @throws Exception
   */
  private String generateExcelFile(List<ContinuousWorkEmployeeExportModel> employees, String plant, String workshop) throws Exception {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    String timestamp = sdf.format(new Date());
    String fileName = String.format("连续工作员工清单_%s_%s_%s.xlsx", plant, workshop, timestamp);
    
    String realPath = uploadPath + File.separator + DateUtil.getDay() + File.separator;
    File folder = new File(realPath);
    if (!folder.exists()) {
      folder.mkdirs();
    }
    
    String filePath = realPath + fileName;
    
    try (FileOutputStream out = new FileOutputStream(filePath)) {
      ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX, true);
      Sheet sheet = new Sheet(1, 0, ContinuousWorkEmployeeExportModel.class);
      sheet.setSheetName(String.format("%s_%s连续工作员工", plant, workshop));
      sheet.setAutoWidth(Boolean.TRUE);
      
      writer.write(employees, sheet);
      writer.finish();
    }
    
    return filePath;
  }
}

