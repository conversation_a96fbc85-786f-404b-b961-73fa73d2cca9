package org.fh.service.common;

import java.util.List;
import org.fh.dto.common.attendance.AttendanceQueryDto;
import org.fh.dto.common.dailyAttendanceAnalysis.DailyAttendanceAnalysisQueryDto;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.util.ResultHelper;
import org.fh.vo.common.DailyAttendanceAnalysisVo;

/**
 * 说明： 每日考勤分析接口 作者：Carl 时间：2023-06-02
 */
public interface DailyAttendanceAnalysisService {

  /**
   * 新增
   *
   * @param pd
   * @throws Exception
   */
  public void save(PageData pd) throws Exception;

  /**
   * 删除
   *
   * @param pd
   * @throws Exception
   */
  public void delete(PageData pd) throws Exception;

  /**
   * 修改
   *
   * @param pd
   * @throws Exception
   */
  public void edit(PageData pd) throws Exception;

  /**
   * 列表
   *
   * @param page
   * @throws Exception
   */
  public List<PageData> list(Page page) throws Exception;

  /**
   * 列表(全部)
   *
   * @param pd
   * @throws Exception
   */
  public List<PageData> listAll(PageData pd) throws Exception;

  /**
   * 通过id获取数据
   *
   * @param pd
   * @throws Exception
   */
  public PageData findById(PageData pd) throws Exception;

  /**
   * 批量删除
   *
   * @param ArrayDATA_IDS
   * @throws Exception
   */
  public void deleteAll(String[] ArrayDATA_IDS) throws Exception;

  public ResultHelper queryAttendanceMonthByKayang(AttendanceQueryDto queryDto) throws Exception;

  public ResultHelper queryAttendanceByKayang(AttendanceQueryDto queryDto) throws Exception;

  public ResultHelper uploadDailyAttendanceAnalysis(List<DailyAttendanceAnalysisVo> list)
      throws Exception;

  void sendMsgToAbnormalAttendances() throws Exception;

  void sendAbnomalAttendanceNoticeMsg() throws Exception;

  List<String> getEmployeesWorkedSixConsecutiveDays(PageData pd) throws Exception;

  /**
   * 提醒连续工作6天的员工
   *
   * @throws Exception
   */
  void remindContinuousWork() throws Exception;

  /**
   * 分页查询每日考勤分析数据
   *
   * @param queryDto 查询条件DTO
   * @return 分页查询结果
   * @throws Exception
   */
  ResultHelper queryDailyAttendanceAnalysisPage(DailyAttendanceAnalysisQueryDto queryDto) throws Exception;
}

