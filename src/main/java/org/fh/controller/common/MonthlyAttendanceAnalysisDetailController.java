package org.fh.controller.common;

import com.alibaba.excel.support.ExcelTypeEnum;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.model.exportModel.MonthlyAttendanceAnalysisDetailExportModel;
import org.fh.security.AuthConstant;
import org.fh.security.Authorize;
import org.fh.service.common.EmployeeService;
import org.fh.service.common.MonthlyAttendanceAnalysisDetailService;

import org.fh.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明：确认详情 作者：FH Admin QQ313596790 时间：2023-06-13 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/admin/monthlyAttendanceAnalysisDetailController")
public class MonthlyAttendanceAnalysisDetailController extends BaseController {

  @Autowired
  private MonthlyAttendanceAnalysisDetailService monthlyAttendanceAnalysisDetailService;

  /**
   * 安全地从PageData获取字符串值，处理BigDecimal等数值类型
   */
  private String getSafeString(PageData pd, String key) {
    Object value = pd.get(key);
    if (value == null) {
      return "";
    }
    if (value instanceof java.math.BigDecimal) {
      return ((java.math.BigDecimal) value).toString();
    }
    return value.toString();
  }

  /**
   * 保存
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/save")
  @RequiresPermissions("monthlyattendanceanalysisdetail:add")
  public String save(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    pd.put("MONTHLYATTENDANCEANALYSISDETAIL_ID", this.get32UUID());  //主键
    monthlyAttendanceAnalysisDetailService.save(pd);
    model.addAttribute("msg", "success");
    return "transferPage";
  }

  /**
   * 删除
   *
   * @param out
   * @throws Exception
   */
  @RequestMapping(value = "/delete")
  @ResponseBody
  @RequiresPermissions("monthlyattendanceanalysisdetail:del")
  public Object delete() {
    Map<String, String> map = new HashMap<String, String>();
    PageData pd = new PageData();
    pd = this.getPageData();
    String errInfo = "success";
    try {
      monthlyAttendanceAnalysisDetailService.delete(pd);
    } catch (Exception e) {
      errInfo = "error";
    }
    map.put("result", errInfo);        //返回结果
    return map;
  }

  /**
   * 修改
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/edit")
  @RequiresPermissions("monthlyattendanceanalysisdetail:edit")
  public String edit(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    monthlyAttendanceAnalysisDetailService.edit(pd);
    model.addAttribute("msg", "success");
    return "transferPage";
  }

  /**
   * 列表
   *
   * @param page
   * @throws Exception
   */
  @RequestMapping(value = "/list")
  @RequiresPermissions("monthlyattendanceanalysisdetail:list")
  public String list(Page page, Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    String KEYWORDS = pd.getString("KEYWORDS");            //关键词检索条件
    if (Tools.notEmpty(KEYWORDS)) {
      pd.put("KEYWORDS", KEYWORDS.trim());
    }
    page.setPd(pd);
    List<PageData> varList = monthlyAttendanceAnalysisDetailService.list(
        page);  //列出MonthlyAttendanceAnalysisDetail列表
    model.addAttribute("varList", varList);
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_list";
  }

  /**
   * 去新增页面
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/goAdd")
  @RequiresPermissions("monthlyattendanceanalysisdetail:add")
  public String goAdd(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    model.addAttribute("msg", "save");
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_edit";
  }

  /**
   * 去修改页面
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/goEdit")
  @RequiresPermissions("monthlyattendanceanalysisdetail:edit")
  public String goEdit(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    pd = monthlyAttendanceAnalysisDetailService.findById(pd);  //根据ID读取
    model.addAttribute("msg", "edit");
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_edit";
  }

  /**
   * 批量删除
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/deleteAll")
  @ResponseBody
  @RequiresPermissions("monthlyattendanceanalysisdetail:del")
  public Object deleteAll() {
    Map<String, Object> map = new HashMap<String, Object>();
    PageData pd = new PageData();
    pd = this.getPageData();
    String errInfo = "success";
    String DATA_IDS = pd.getString("DATA_IDS");
    try {
      if (Tools.notEmpty(DATA_IDS)) {
        String ArrayDATA_IDS[] = DATA_IDS.split(",");
        monthlyAttendanceAnalysisDetailService.deleteAll(ArrayDATA_IDS);
        errInfo = "success";
      } else {
        errInfo = "error";
      }
    } catch (Exception e) {
      errInfo = "error";
    }
    map.put("result", errInfo);        //返回结果
    return map;
  }

  /**
   * 导出到excel
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/excel")
  @RequiresPermissions("toExcel")
  public ModelAndView exportExcel() throws Exception {
    ModelAndView mv = new ModelAndView();
    PageData pd = new PageData();
    pd = this.getPageData();
    Map<String, Object> dataMap = new HashMap<String, Object>();
    List<String> titles = new ArrayList<String>();
    titles.add("备注1");  //1
    titles.add("备注2");  //2
    titles.add("备注3");  //3
    titles.add("1:已确认，0：未确认");  //4
    dataMap.put("titles", titles);
    List<PageData> varOList = monthlyAttendanceAnalysisDetailService.listAll(pd);
    List<PageData> varList = new ArrayList<PageData>();
    for (int i = 0; i < varOList.size(); i++) {
      PageData vpd = new PageData();
      vpd.put("var1",
          varOList.get(i).getString("BIZ_MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID"));      //1
      vpd.put("var2", varOList.get(i).getString("TERM"));      //2
      vpd.put("var3", varOList.get(i).getString("EMP_ID"));      //3
      vpd.put("var4", varOList.get(i).get("CONFIRM_STATUS").toString());  //4
      varList.add(vpd);
    }
    dataMap.put("varList", varList);
    ObjectExcelView erv = new ObjectExcelView();
    mv = new ModelAndView(erv, dataMap);
    return mv;
  }

  @PostMapping(value = "/queryPage")
  @Authorize(value = {AuthConstant.CURRENT_USER_HEADER})
  @ResponseBody
  public ResultHelper queryPage(@RequestBody MonthlyAttendanceAnalysisDetailQueryDto query) {
    ResultHelper result = ResultHelper.builder().build();
    try {
      query.setLoginUser(getUsername());
      return monthlyAttendanceAnalysisDetailService
          .queryPage(query);

    } catch (Exception e) {
      result.setMessage("分页查询考勤汇总信息异常");
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      return result;
    }
  }

  /**
   * 导出月度考勤分析详情Excel
   *
   * @param query 查询条件
   * @param response HTTP响应
   */
  @PostMapping(value = "/exportExcel")
  @Authorize(value = {AuthConstant.CURRENT_USER_HEADER})
  @ResponseBody
  public void exportExcel(@RequestBody MonthlyAttendanceAnalysisDetailQueryDto query, 
                          HttpServletResponse response) {
    try {
      // 查询所有符合条件的数据
      query.setLoginUser(getUsername());
      List<PageData> dataList = monthlyAttendanceAnalysisDetailService.queryAllForExport(query);
      
      // 转换为导出模型
      List<MonthlyAttendanceAnalysisDetailExportModel> exportList = new ArrayList<>();
      for (PageData pd : dataList) {
        MonthlyAttendanceAnalysisDetailExportModel exportModel = new MonthlyAttendanceAnalysisDetailExportModel();
        exportModel.setYm(pd.getString("ym"));
        // 处理Timestamp类型的sendTime字段
        Object sendTimeObj = pd.get("sendTime");
        if (sendTimeObj != null) {
          if (sendTimeObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) sendTimeObj;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            exportModel.setSendTime(dateFormat.format(timestamp));
          } else {
            exportModel.setSendTime(sendTimeObj.toString());
          }
        } else {
          exportModel.setSendTime("");
        }

        // 处理Timestamp类型的confirmTime字段
        Object confirmTimeObj = pd.get("confirmTime");
        if (confirmTimeObj != null) {
          if (confirmTimeObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) confirmTimeObj;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            exportModel.setConfirmTime(dateFormat.format(timestamp));
          } else {
            exportModel.setConfirmTime(confirmTimeObj.toString());
          }
        } else {
          exportModel.setConfirmTime("");
        }

        exportModel.setName(pd.getString("name"));
        exportModel.setEmpId(pd.getString("empId"));
        exportModel.setPlant(pd.getString("plant"));
        exportModel.setDepartment(pd.getString("productionLine"));
//        exportModel.setProductionLine(pd.getString("productionLine"));
        exportModel.setWorkTeam(pd.getString("workTeam"));
//        exportModel.setActived(pd.getString("actived"));
        exportModel.setPnhr(getSafeString(pd, "pnhr"));
        exportModel.setWkhr(getSafeString(pd, "wkhr"));
        exportModel.setAdhr(getSafeString(pd, "adhr"));
        exportModel.setUahr(getSafeString(pd, "uahr"));
        exportModel.setLimn(getSafeString(pd, "limn"));
        exportModel.setEomn(getSafeString(pd, "eomn"));
        exportModel.setOt1(getSafeString(pd, "an15"));
        exportModel.setOt2(getSafeString(pd, "an20"));
        exportModel.setOt3(getSafeString(pd, "ot3"));

        // ot1,ot2,ot3累加获取overtimeAll
        try {
          Object ot1Obj = pd.get("an15");
          Object ot2Obj = pd.get("an20");
          Object ot3Obj = pd.get("ot3");

          BigDecimal ot1 = (ot1Obj instanceof BigDecimal) ? (BigDecimal) ot1Obj :
                          (ot1Obj != null ? new BigDecimal(ot1Obj.toString()) : BigDecimal.ZERO);
          BigDecimal ot2 = (ot2Obj instanceof BigDecimal) ? (BigDecimal) ot2Obj :
                          (ot2Obj != null ? new BigDecimal(ot2Obj.toString()) : BigDecimal.ZERO);
          BigDecimal ot3 = (ot3Obj instanceof BigDecimal) ? (BigDecimal) ot3Obj :
                          (ot3Obj != null ? new BigDecimal(ot3Obj.toString()) : BigDecimal.ZERO);

          BigDecimal overtimeTotal = ot1.add(ot2).add(ot3);
          exportModel.setOvertimeTotal(overtimeTotal.toString());
        } catch (Exception e) {
          // 如果计算出错，设置为空字符串
          exportModel.setOvertimeTotal("");
        }
        exportModel.setPerl(getSafeString(pd, "perl"));
        exportModel.setOtcl(getSafeString(pd, "otcl"));
        exportModel.setOccl(getSafeString(pd, "occl"));
        exportModel.setAnnl(getSafeString(pd, "annl"));
        exportModel.setPskl(getSafeString(pd, "pskl"));
        exportModel.setBpsl(getSafeString(pd, "bpsl"));
        exportModel.setIsjl(getSafeString(pd, "isjl"));
        exportModel.setComl(getSafeString(pd, "coml"));
        exportModel.setFanf(getSafeString(pd, "fanf"));
        exportModel.setMarl(getSafeString(pd, "marl"));
        exportModel.setMlpl(getSafeString(pd, "mlpl"));
        exportModel.setPatl(getSafeString(pd, "patl"));
        exportModel.setMatl(getSafeString(pd, "matl"));
        exportModel.setDsfl(getSafeString(pd, "dsfl"));
        exportModel.setBrfl(getSafeString(pd, "brfl"));
        exportModel.setCifl(getSafeString(pd, "cifl"));
        exportModel.setFpgl(getSafeString(pd, "fpgl"));
        exportModel.setMscl(getSafeString(pd, "mscl"));
        exportModel.setPofl(getSafeString(pd, "pofl"));
        exportModel.setLadl(getSafeString(pd, "ladl"));
        exportModel.setChildl(getSafeString(pd, "childl"));
        exportModel.setNursl(getSafeString(pd, "nursl"));
        exportModel.setNbos(getSafeString(pd, "nbos"));
        exportModel.setLsch(getSafeString(pd, "lsch"));

        exportList.add(exportModel);
      }
      
      // 生成文件名
      String fileName = String.format("月度考勤分析详情_%s", DateUtil.getDay());
      
      // 使用BaseController的setResponse方法导出Excel
      setResponse(response, exportList, fileName);
      
    } catch (Exception e) {
      e.printStackTrace();
      // 设置错误响应
      response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
      try {
        response.getWriter().write("导出失败：" + e.getMessage());
      } catch (Exception ex) {
        ex.printStackTrace();
      }
    }
  }


}
