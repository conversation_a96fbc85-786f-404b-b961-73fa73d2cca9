package org.fh.controller.common;

import com.alibaba.excel.support.ExcelTypeEnum;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.model.exportModel.MonthlyAttendanceAnalysisDetailExportModel;
import org.fh.security.AuthConstant;
import org.fh.security.Authorize;
import org.fh.service.common.MonthlyAttendanceAnalysisDetailService;

import org.fh.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明：确认详情 作者：FH Admin QQ313596790 时间：2023-06-13 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/admin/monthlyAttendanceAnalysisDetailController")
public class MonthlyAttendanceAnalysisDetailController extends BaseController {

  @Autowired
  private MonthlyAttendanceAnalysisDetailService monthlyAttendanceAnalysisDetailService;

  /**
   * 保存
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/save")
  @RequiresPermissions("monthlyattendanceanalysisdetail:add")
  public String save(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    pd.put("MONTHLYATTENDANCEANALYSISDETAIL_ID", this.get32UUID());  //主键
    monthlyAttendanceAnalysisDetailService.save(pd);
    model.addAttribute("msg", "success");
    return "transferPage";
  }

  /**
   * 删除
   *
   * @param out
   * @throws Exception
   */
  @RequestMapping(value = "/delete")
  @ResponseBody
  @RequiresPermissions("monthlyattendanceanalysisdetail:del")
  public Object delete() {
    Map<String, String> map = new HashMap<String, String>();
    PageData pd = new PageData();
    pd = this.getPageData();
    String errInfo = "success";
    try {
      monthlyAttendanceAnalysisDetailService.delete(pd);
    } catch (Exception e) {
      errInfo = "error";
    }
    map.put("result", errInfo);        //返回结果
    return map;
  }

  /**
   * 修改
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/edit")
  @RequiresPermissions("monthlyattendanceanalysisdetail:edit")
  public String edit(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    monthlyAttendanceAnalysisDetailService.edit(pd);
    model.addAttribute("msg", "success");
    return "transferPage";
  }

  /**
   * 列表
   *
   * @param page
   * @throws Exception
   */
  @RequestMapping(value = "/list")
  @RequiresPermissions("monthlyattendanceanalysisdetail:list")
  public String list(Page page, Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    String KEYWORDS = pd.getString("KEYWORDS");            //关键词检索条件
    if (Tools.notEmpty(KEYWORDS)) {
      pd.put("KEYWORDS", KEYWORDS.trim());
    }
    page.setPd(pd);
    List<PageData> varList = monthlyAttendanceAnalysisDetailService.list(
        page);  //列出MonthlyAttendanceAnalysisDetail列表
    model.addAttribute("varList", varList);
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_list";
  }

  /**
   * 去新增页面
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/goAdd")
  @RequiresPermissions("monthlyattendanceanalysisdetail:add")
  public String goAdd(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    model.addAttribute("msg", "save");
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_edit";
  }

  /**
   * 去修改页面
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/goEdit")
  @RequiresPermissions("monthlyattendanceanalysisdetail:edit")
  public String goEdit(Model model) throws Exception {
    PageData pd = new PageData();
    pd = this.getPageData();
    pd = monthlyAttendanceAnalysisDetailService.findById(pd);  //根据ID读取
    model.addAttribute("msg", "edit");
    model.addAttribute("pd", pd);
    return "common/monthlyattendanceanalysisdetail/monthlyattendanceanalysisdetail_edit";
  }

  /**
   * 批量删除
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/deleteAll")
  @ResponseBody
  @RequiresPermissions("monthlyattendanceanalysisdetail:del")
  public Object deleteAll() {
    Map<String, Object> map = new HashMap<String, Object>();
    PageData pd = new PageData();
    pd = this.getPageData();
    String errInfo = "success";
    String DATA_IDS = pd.getString("DATA_IDS");
    try {
      if (Tools.notEmpty(DATA_IDS)) {
        String ArrayDATA_IDS[] = DATA_IDS.split(",");
        monthlyAttendanceAnalysisDetailService.deleteAll(ArrayDATA_IDS);
        errInfo = "success";
      } else {
        errInfo = "error";
      }
    } catch (Exception e) {
      errInfo = "error";
    }
    map.put("result", errInfo);        //返回结果
    return map;
  }

  /**
   * 导出到excel
   *
   * @param
   * @throws Exception
   */
  @RequestMapping(value = "/excel")
  @RequiresPermissions("toExcel")
  public ModelAndView exportExcel() throws Exception {
    ModelAndView mv = new ModelAndView();
    PageData pd = new PageData();
    pd = this.getPageData();
    Map<String, Object> dataMap = new HashMap<String, Object>();
    List<String> titles = new ArrayList<String>();
    titles.add("备注1");  //1
    titles.add("备注2");  //2
    titles.add("备注3");  //3
    titles.add("1:已确认，0：未确认");  //4
    dataMap.put("titles", titles);
    List<PageData> varOList = monthlyAttendanceAnalysisDetailService.listAll(pd);
    List<PageData> varList = new ArrayList<PageData>();
    for (int i = 0; i < varOList.size(); i++) {
      PageData vpd = new PageData();
      vpd.put("var1",
          varOList.get(i).getString("BIZ_MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID"));      //1
      vpd.put("var2", varOList.get(i).getString("TERM"));      //2
      vpd.put("var3", varOList.get(i).getString("EMP_ID"));      //3
      vpd.put("var4", varOList.get(i).get("CONFIRM_STATUS").toString());  //4
      varList.add(vpd);
    }
    dataMap.put("varList", varList);
    ObjectExcelView erv = new ObjectExcelView();
    mv = new ModelAndView(erv, dataMap);
    return mv;
  }

  @PostMapping(value = "/queryPage")
  @Authorize(value = {AuthConstant.CURRENT_USER_HEADER})
  @ResponseBody
  public ResultHelper queryPage(@RequestBody MonthlyAttendanceAnalysisDetailQueryDto query) {
    ResultHelper result = ResultHelper.builder().build();
    try {

      return monthlyAttendanceAnalysisDetailService
          .queryPage(query);

    } catch (Exception e) {
      result.setMessage("分页查询考勤汇总信息异常");
      result.setCode(ResultCode.FAILURE);
      result.setStatus(1);
      return result;
    }
  }

  /**
   * 导出月度考勤分析详情Excel
   *
   * @param query 查询条件
   * @param response HTTP响应
   */
  @PostMapping(value = "/exportExcel")
  @Authorize(value = {AuthConstant.CURRENT_USER_HEADER})
  @ResponseBody
  public void exportExcel(@RequestBody MonthlyAttendanceAnalysisDetailQueryDto query, 
                          HttpServletResponse response) {
    try {
      // 查询所有符合条件的数据
      List<PageData> dataList = monthlyAttendanceAnalysisDetailService.queryAllForExport(query);
      
      // 转换为导出模型
      List<MonthlyAttendanceAnalysisDetailExportModel> exportList = new ArrayList<>();
      for (PageData pd : dataList) {
        MonthlyAttendanceAnalysisDetailExportModel exportModel = new MonthlyAttendanceAnalysisDetailExportModel();

        // 处理Timestamp类型的sendTime字段
        Object sendTimeObj = pd.get("sendTime");
        if (sendTimeObj != null) {
          if (sendTimeObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) sendTimeObj;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            exportModel.setSendTime(dateFormat.format(timestamp));
          } else {
            exportModel.setSendTime(sendTimeObj.toString());
          }
        } else {
          exportModel.setSendTime("");
        }

        exportModel.setConfirmTime(pd.getString("confirmTime"));
        exportModel.setName(pd.getString("name"));
        exportModel.setEmpId(pd.getString("empId"));
        exportModel.setPlant(pd.getString("plant"));
        exportModel.setDepartment(pd.getString("department"));
//        exportModel.setProductionLine(pd.getString("productionLine"));
        exportModel.setWorkTeam(pd.getString("workTeam"));
//        exportModel.setActived(pd.getString("actived"));
        exportModel.setPnhr(pd.getString("pnhr"));
        exportModel.setWkhr(pd.getString("wkhr"));
        exportModel.setAdhr(pd.getString("adhr"));
        exportModel.setUahr(pd.getString("uahr"));
        exportModel.setLimn(pd.getString("limn"));
        exportModel.setEomn(pd.getString("eomn"));
        exportModel.setOt1(pd.getString("ot1"));
        exportModel.setOt2(pd.getString("ot2"));
        exportModel.setOt3(pd.getString("ot3"));
        // ot1,ot2,ot3累加获取overtimeAll
        exportModel.setOvertimeTotal("");
        exportModel.setPerl(pd.getString("perl"));
        exportModel.setOtcl(pd.getString("otcl"));
        exportModel.setOccl(pd.getString("occl"));
        exportModel.setAnnl(pd.getString("annl"));
        exportModel.setPskl(pd.getString("pskl"));
        exportModel.setBpsl(pd.getString("bpsl"));
        exportModel.setIsjl(pd.getString("isjl"));
        exportModel.setComl(pd.getString("coml"));
        exportModel.setFanf(pd.getString("fanf"));
        exportModel.setMarl(pd.getString("marl"));
        exportModel.setMlpl(pd.getString("mlpl"));
        exportModel.setPatl(pd.getString("patl"));
        exportModel.setMatl(pd.getString("matl"));
        exportModel.setDsfl(pd.getString("dsfl"));
        exportModel.setBrfl(pd.getString("brfl"));
        exportModel.setCifl(pd.getString("cifl"));
        exportModel.setFpgl(pd.getString("fpgl"));
        exportModel.setMscl(pd.getString("mscl"));
        exportModel.setPofl(pd.getString("pofl"));
        exportModel.setLadl(pd.getString("ladl"));
        exportModel.setChildl(pd.getString("childl"));
        exportModel.setNursl(pd.getString("nursl"));
        exportModel.setNbos(pd.getString("nbos"));
        exportModel.setLsch(pd.getString("lsch"));
        
        // 处理Timestamp类型的confirmTime字段
        Object confirmTimeObj = pd.get("confirmTime");
        if (confirmTimeObj != null) {
          if (confirmTimeObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) confirmTimeObj;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            exportModel.setConfirmTime(dateFormat.format(timestamp));
          } else {
            exportModel.setConfirmTime(confirmTimeObj.toString());
          }
        } else {
          exportModel.setConfirmTime("");
        }
        exportList.add(exportModel);
      }
      
      // 生成文件名
      String fileName = String.format("月度考勤分析详情_%s", DateUtil.getDay());
      
      // 使用BaseController的setResponse方法导出Excel
      setResponse(response, exportList, fileName);
      
    } catch (Exception e) {
      e.printStackTrace();
      // 设置错误响应
      response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
      try {
        response.getWriter().write("导出失败：" + e.getMessage());
      } catch (Exception ex) {
        ex.printStackTrace();
      }
    }
  }


}
