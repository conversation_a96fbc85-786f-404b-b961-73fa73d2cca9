package org.fh.model.exportModel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

/**
 * 月度考勤分析详情导出模型
 * <AUTHOR>
 * @date 2024
 */
@Data
public class MonthlyAttendanceAnalysisDetailExportModel extends BaseRowModel {

    @ExcelProperty(value = "姓名", index = 0)
    private String name; // 姓名

    @ExcelProperty(value = "工号", index = 1)
    private String empId; // 工号

    @ExcelProperty(value = "部门", index = 2)
    private String department; // 部门

    @ExcelProperty(value = "生产线", index = 3)
    private String productionLine; // 生产线

    @ExcelProperty(value = "班组", index = 4)
    private String workTeam; // 班组

    @ExcelProperty(value = "在职状态", index = 5)
    private String actived; // 状态

    @ExcelProperty(value = "确认时间", index = 6)
    private String confirmTime; // 确认时间
}