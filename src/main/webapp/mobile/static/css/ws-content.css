@charset "utf-8";
/*内容查询*/
.sear-header { width:100%; padding-top:.2rem; padding-bottom:.2rem; background-color:#fff; }
.sear-wk { width:90%; margin:0 auto; height:.6rem;}
.sear-wk .sear-search { width:80%; height:.6rem; box-sizing:border-box; float:left; padding-top:.1rem; padding-bottom:.1rem; border:.02rem solid #42e0f6; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem;}
.sear-wk .sear-search ul li:first-of-type { width:85%; height:.4rem; line-height:.4rem; float:left; }
.sear-wk .sear-search ul li:first-of-type input::-webkit-input-placeholder {color: #999999;　}
.sear-wk .sear-search ul li:first-of-type input:-moz-placeholder {color:#999999;}
.sear-wk .sear-search ul li:first-of-type input::-moz-placeholder {color:#999999;}
.sear-wk .sear-search ul li:first-of-type input:-ms-input-placeholder {color:#999999;}
.sear-wk .sear-search ul li:first-of-type input { border:0; outline:none; color:#999999; font-size:.2rem; text-indent:.2rem;}
.sear-wk .sear-search ul li:last-of-type { width:15%; float:right; height:.4rem; line-height:.46rem; text-align:center;}
.sear-wk .sear-search ul li:last-of-type i { font-size:.38rem; color:#42e0f6;}
.sear-wk .sear-search ul::after { content:""; display:block; clear:both;}
.sear-wk .sear-tjk { width:20%; float:right;}
.sear-tjk .sear-tj { width:80%; height:.6rem; line-height:.66rem; float:right; text-align:center;}
.sear-tjk .sear-tj i {font-size:.38rem; color:#42e0f6;}
.sear-wk::after {content:""; display:block; clear:both;}

.guishu-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.guishu-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.guishu-cont-wk .guishu-wz p {width:100%; box-sizing:border-box;}
.guishu-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.guishu-wz { width:100%;}
.guishu-wz p { width:100%; height:.4rem; line-height:.4rem;}
.color-hui1 {color:#585858; font-size:.22rem;}
.guishu-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.guishu-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.guishu-cont-wk ul li:last-of-type a i { font-size:.34rem;}
.guishu-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.dlslb-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.dlslb-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.dlslb-cont-wk .dlslb-wz p:first-of-type {width:50%; float:left; box-sizing:border-box;}
.dlslb-cont-wk .dlslb-wz p:last-of-type {width:50%; float:right; box-sizing:border-box;}
.dlslb-wz::after { content:""; display:block; clear:both;}
.dlslb-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.dlslb-wz { width:100%;}
.dlslb-wz p { width:100%; height:.4rem; line-height:.4rem;}
.dlslb-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.dlslb-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.dlslb-cont-wk ul li:last-of-type a:first-of-type { width:25%;}
.dlslb-cont-wk ul li:last-of-type a i { font-size:.34rem;}
.dlslb-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.dlsxx-cont { width:100%; background-color:#fff;}
.dlsxx-bt { width:100%; height:.7rem; box-sizing:border-box; padding:.1rem 0; background-color:#f0f0f0; }
.dlsxx-bt p {width:90%; height:.5rem; line-height:.5rem; font-size:.24rem; margin:0 auto; color:#585858;}
.dlsxx-lb {width:100%; box-sizing:border-box; background-color:#fff; }
.dlsxx-lb form {width:90%; height:.7rem; font-size:.2rem; margin:0 auto; border-bottom:.02rem solid #f0f0f0; padding:.1rem 0; box-sizing:border-box;}
.dlsxx-lb form label { width:25%; height:.5rem; line-height:.5rem; float:left; color:#585858;}
.dlsxx-lb form input { display:block; width:75%; height:.5rem; box-sizing:border-box; padding:.1rem; line-height:.5rem; float:right; border:0; text-align:right; outline:none; color:#999999; word-break:keep-all; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.dlsxx-lb form:last-of-type { border:0;}

.tjdlsxx-cont { width:100%; display:block; background-color:#fff; margin-bottom:.94rem;}
.tjdlsxx-bt { width:100%; height:.7rem; box-sizing:border-box; padding:.1rem 0; background-color:#f0f0f0; }
.tjdlsxx-bt p {width:90%; height:.5rem; line-height:.5rem; font-size:.24rem; margin:0 auto; color:#585858;}
.tjdlsxx-lb {width:100%; box-sizing:border-box; background-color:#fff; }
.tjdlsxx-lb form {width:90%; height:.7rem; font-size:.2rem; margin:0 auto; border-bottom:.02rem solid #f0f0f0; padding:.1rem 0; box-sizing:border-box;}
.tjdlsxx-lb form label { width:25%; height:.5rem; line-height:.5rem; float:left; color:#585858;}
.tjdlsxx-lb form input { display:block; width:75%; height:.5rem; box-sizing:border-box; padding:.1rem; line-height:.5rem; float:right; border:0; text-align:right; outline:none; color:#999999; word-break:keep-all; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.tjdlsxx-lb form:last-of-type { border:0;}
.tjdlsxx-lb form::after { content:""; display:block; clear:both;}
.dlstj-butt { width:100%; position:fixed; bottom:0; z-index:99;}
.dlstj-butt a { display:block; width:100%; height:.8rem; line-height:.8rem; background-color:#00dbf5; color:#fff; font-size:.24rem; text-align:center;}

.bjdlsxx-cont { width:100%; display:block; background-color:#fff; margin-bottom:.94rem;}
.bjdlsxx-bt { width:100%; height:.7rem; box-sizing:border-box; padding:.1rem 0; background-color:#f0f0f0; }
.bjdlsxx-bt p {width:90%; height:.5rem; line-height:.5rem; font-size:.24rem; margin:0 auto; color:#585858;}
.bjdlsxx-lb {width:100%; box-sizing:border-box; background-color:#fff; }
.bjdlsxx-lb form {width:90%; height:.7rem; font-size:.2rem; margin:0 auto; border-bottom:.02rem solid #f0f0f0; padding:.1rem 0; box-sizing:border-box;}
.bjdlsxx-lb form label { width:25%; height:.5rem; line-height:.5rem; float:left; color:#585858;}
.bjdlsxx-lb form input { display:block; width:75%; height:.5rem; box-sizing:border-box; padding:.1rem; line-height:.5rem; float:right; border:0; text-align:right; outline:none; color:#999999; word-break:keep-all; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.bjdlsxx-lb form:last-of-type { border:0;}
.bjdlsxx-lb form::after { content:""; display:block; clear:both;}
.dlsbj-butt { width:100%; position:fixed; bottom:0; z-index:99;}
.dlsbj-butt a { display:block; width:100%; height:.8rem; line-height:.8rem; background-color:#00dbf5; color:#fff; font-size:.24rem; text-align:center;}

.dlsshdz-cont { width:100%; margin-bottom:.94rem;}
.dlsshdz-cont-wk { width:100%; background-color:#fff; margin-bottom:.16rem;}
.dlsshdz-cont-wk ul {width:90%; box-sizing:border-box; margin:0 auto; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.dlsshdz-cont-wk .dlsshdz-wz p {width:100%; box-sizing:border-box;}
.dlsshdz-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.dlsshdz-wz { width:100%;}
.dlsshdz-wz p { width:100%; padding:.02rem 0;}
.dlsshdz-wz p span:nth-child(1) {display:inline-block; width:30%; height:.4rem; line-height:.4rem; float:left; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;}
.dlsshdz-wz p span:nth-child(2) {display:inline-block; width:70%; height:.4rem; line-height:.4rem; float:right; overflow:hidden; white-space:nowrap; text-overflow:ellipsis; text-align:right;}
.dlsshdz-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.dlsshdz-z { display:inline-block; width:40%; float:left; }
.dlsshdz-z a {display:inline-block; width:100%; height:.44rem; line-height:.44rem; font-size:.22rem;}
.dlsshdz-z a i {display:inline-block; width:20%; height:.44rem; line-height:.44rem; font-size:.36rem;}
.dlsshdz-y { display:inline-block; width:40%; float:right;}
.dlsshdz-y a { display:inline-block; width:50%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.dlsshdz-y a i { font-size:.34rem;}
.dlsshdz-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}
.dlsshdz-butt { width:100%; position:fixed; bottom:0; z-index:99;}
.dlsshdz-butt a { display:block; width:100%; height:.8rem; line-height:.8rem; background-color:#00dbf5; color:#fff; font-size:.24rem; text-align:center;}

.tjshdz-cont { width:100%; background-color:#fff;}
.tjshdz-bt { width:100%; height:.7rem; box-sizing:border-box; padding:.1rem 0; background-color:#fff; margin-top:.16rem;}
.tjshdz-bt p {width:90%; height:.5rem; line-height:.5rem; font-size:.24rem; margin:0 auto;}
.tjshdz-lb {width:100%; box-sizing:border-box; background-color:#fff; }
.tjshdz-lb form {width:90%; height:.7rem; font-size:.2rem; margin:0 auto; border-bottom:.02rem solid #f0f0f0; padding:.1rem 0; box-sizing:border-box;}
.tjshdz-lb form label { width:25%; height:.5rem; line-height:.5rem; float:left; color:#585858;}
.tjshdz-lb form input { display:block; width:75%; height:.5rem; box-sizing:border-box; padding:.1rem; line-height:.5rem; float:right; border:0; text-align:right; outline:none; color:#999999; word-break:keep-all; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.tjshdz-lb form:last-of-type { border:0;}
.tjshdz-right { display:inline-block; height:.5rem; line-height:.56rem; float:right; font-size:.34rem;}
.tjshdz-butt { width:100%; position:fixed; bottom:0; z-index:99;}
.tjshdz-butt a { display:block; width:100%; height:.8rem; line-height:.8rem; background-color:#00dbf5; color:#fff; font-size:.24rem; text-align:center;}

.bjshdz-cont { width:100%; background-color:#fff;}
.bjshdz-bt { width:100%; height:.7rem; box-sizing:border-box; padding:.1rem 0; background-color:#fff; margin-top:.16rem;}
.bjshdz-bt p {width:90%; height:.5rem; line-height:.5rem; font-size:.24rem; margin:0 auto;}
.bjshdz-lb {width:100%; box-sizing:border-box; background-color:#fff; }
.bjshdz-lb .ffrom {width:90%; height:.7rem; font-size:.2rem; margin:0 auto; border-bottom:.02rem solid #f0f0f0; padding:.1rem 0; box-sizing:border-box;}
.bjshdz-lb .ffrom label { width:25%; height:.5rem; line-height:.5rem; float:left; color:#585858;}
.bjshdz-lb .ffrom input { display:block; width:75%; height:.5rem; box-sizing:border-box; padding:.1rem; line-height:.5rem; float:right; border:0; text-align:right; outline:none; color:#999999; word-break:keep-all; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.bjshdz-lb .ffrom:last-of-type { border:0;}
.bjshdz-right { display:inline-block; height:.5rem; line-height:.56rem; float:right; font-size:.34rem;}
.bjshdz-butt { width:100%; position:fixed; bottom:0; z-index:99;}
.bjshdz-butt a { display:block; width:100%; height:.8rem; line-height:.8rem; background-color:#00dbf5; color:#fff; font-size:.24rem; text-align:center;}

.splist-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.splist-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.splist-cont-wk .splist-wz p:first-of-type {width:50%; float:left; box-sizing:border-box;}
.splist-cont-wk .splist-wz p:last-of-type {width:50%; float:right; box-sizing:border-box;}
.splist-wz::after { content:""; display:block; clear:both;}
.splist-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.splist-wz { width:100%;}
.splist-wz p { width:100%; height:.4rem; line-height:.4rem;}
.splist-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.splist-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.splist-cont-wk ul li:last-of-type a::after { content:""; display:block; clear:both;}
.splist-cont-wk ul li:last-of-type a:first-of-type { width:25%;}
.splist-cont-wk ul li:last-of-type a i { font-size:.34rem; float:left;}
.splist-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.splist-cont-wkpa { text-align:center;width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem;padding-bottom:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.splist-cont-wkpa ul li{ display:inline;}

.cpgl-splb-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.cpgl-splb-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.cpgl-splb-cont-wk .cpgl-splb-wz p {width:100%; box-sizing:border-box;}
.cpgl-splb-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.cpgl-splb-wz { width:100%;}
.cpgl-splb-wz p { width:100%; height:.4rem; line-height:.4rem;}
.cpgl-splb-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.cpgl-splb-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.cpgl-splb-cont-wk ul li:last-of-type a i { font-size:.34rem;}
.cpgl-splb-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.cpgl-ppgl-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.cpgl-ppgl-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.cpgl-ppgl-cont-wk .cpgl-ppgl-wz p {width:100%; box-sizing:border-box;}
.cpgl-ppgl-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.cpgl-ppgl-wz { width:100%;}
.cpgl-ppgl-wz p { width:100%; height:.4rem; line-height:.4rem;}
.cpgl-ppgl-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.cpgl-ppgl-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.cpgl-ppgl-cont-wk ul li:last-of-type a i { font-size:.34rem;}
.cpgl-ppgl-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.cpgl-jldw-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.cpgl-jldw-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.cpgl-jldw-cont-wk .cpgl-jldw-wz p {width:100%; box-sizing:border-box;}
.cpgl-jldw-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.cpgl-jldw-wz { width:100%;}
.cpgl-jldw-wz p { width:100%; height:.4rem; line-height:.4rem;}
.cpgl-jldw-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.cpgl-jldw-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.cpgl-jldw-cont-wk ul li:last-of-type a i { font-size:.34rem;}
.cpgl-jldw-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.cpgl-spxx-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.cpgl-spxx-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; padding:.2rem; font-size:.2rem; color:#999999;}
.cpgl-spxx-cont-wk p { width:100%; box-sizing:border-box; padding:.02rem 0;}
.cpgl-spxx-tp {width:100%; box-sizing:border-box; background-color:#fff; margin-top:.16rem;}
.cpgl-spxx-bt { width:100%; height:.6rem; line-height:.6rem; color:#fff; font-size:.22rem; text-align:center; background-color:#00dbf5;}
.cpgl-spxx-nr { width:100%; box-sizing:border-box; padding:.2rem; font-size:.2rem;}

.kcglkc-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.kcglkc-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.kcglkc-wz { width:100%;}
.kcglkc-wz p { width:100%; height:.4rem; line-height:.4rem;}
.kcglkc-wz p:first-of-type {width:50%; float:left; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglkc-wz p:last-of-type {width:50%; float:right; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglkc-wz::after { content:""; display:block; clear:both;}
.kcglkc-wzb p{width:100%; height:.4rem; line-height:.4rem; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:.2rem;}

/*库存管理-入库*/
.kcglrk-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.kcglrk-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.kcglrk-wz { width:100%;}
.kcglrk-wz p { width:100%; height:.4rem; line-height:.4rem;}
.kcglrk-wz p:first-of-type {width:50%; float:left; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglrk-wz p:last-of-type {width:50%; float:right; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglrk-wz::after { content:""; display:block; clear:both;}
.rmb { color:#ff6339; font-size:.22rem;}
.kcglrk-wzb p{width:100%; height:.4rem; line-height:.4rem; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:.2rem;}

 /*库存管理-出库*/
.kcglck-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.kcglck-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.kcglck-wz { width:100%;}
.kcglck-wz p { width:100%; height:.4rem; line-height:.4rem;}
.kcglck-wz p:first-of-type {width:50%; float:left; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglck-wz p:last-of-type {width:50%; float:right; box-sizing:border-box; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.kcglck-wz::after { content:""; display:block; clear:both;}

/*出售管理-在售商品*/
.csgl-zssp-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.csgl-zssp-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.csgl-zssp-cont-wk .csgl-zssp-wz p {width:100%; box-sizing:border-box;}
.csgl-zssp-cont-wk ul li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.csgl-zssp-wz { width:100%;}
.csgl-zssp-wz p { width:100%; height:.4rem; line-height:.4rem;}
.csgl-zssp-cont-wk ul li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.csgl-zssp-cont-wk ul li:last-of-type a { display:inline-block; width:20%; height:.44rem; line-height:.44rem; float:left; font-size:.22rem;}
.csgl-zssp-cont-wk ul li:last-of-type a::after { content:""; display:block; clear:both;}
.csgl-zssp-cont-wk ul li:last-of-type a:first-of-type { width:25%;}
.csgl-zssp-cont-wk ul li:last-of-type a i { font-size:.34rem; float:left;}
.csgl-zssp-cont-wk ul li:last-of-type::after { content:""; display:block; clear:both;}

.csgl-xsjl-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.csgl-xsjl-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.csgl-xsjl-cont-wk .csgl-xsjl-wz p:first-of-type {width:50%; box-sizing:border-box; float:left; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.csgl-xsjl-cont-wk .csgl-xsjl-wz p:last-of-type {width:50%; box-sizing:border-box; float:right; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.csgl-xsjl-cont-wk li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.csgl-xsjl-wz { width:100%; height:.3rem; line-height:.3rem;}
.csgl-xsjl-wz::after { content:""; display:block; clear:both;}
.csgl-xsjl-color1 {color:#585858; font-size:.22rem;}
.csgl-xsjl-color2 {color:#42e0f6; font-size:.22rem;}
.csgl-xsjl-color3 {color:#42e0f6;}
.csgl-xsjl-color4 {color:#f74343;}
.csgl-xsjl-xhx {color:#ff6339;}
.csgl-xsjl-cont-wk li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.csgl-xsjl-cont-wk li:last-of-type p:first-of-type {width:50%; height:.5rem; line-height:.5rem; float:left; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.csgl-xsjl-butt { width:50%; float:right;}
.csgl-xsjl-cont-wk li:last-of-type::after { content:""; display:block; clear:both;}
.csgl-xsjl-cont-wk li .csgl-xsjl-butt a { display:inline-block; width:60%; height:.4rem; line-height:.42rem; text-align:center; border:.02rem solid #42e0f6; border-radius:0.2rem; -webkit-border-radius:0.2rem; -moz-border-radius:0.2rem; color:#42e0f6; font-size:.22rem; }

.csgl-ddxq-cont { width:100%; box-sizing:border-box; padding-top:.1rem; background-color:#fff;}
.csgl-ddxq-cont ul li { width:100%; box-sizing:border-box; padding:.2rem 0; border-bottom:.02rem solid #f0f0f0;}
.csgl-ddxq-contk { width:90%; margin:0 auto; box-sizing:border-box; }
.csgl-ddxq-contk p:first-of-type { width:100%; font-size:.24rem; color:#585858; padding:.02rem 0;}
.csgl-ddxq-contk p:last-of-type { width:100%; font-size:.2rem; color:#999999; padding:.02rem 0;}
.ddxq-danhao { width:80%; float:left;}
.ddxq-danhao p:first-of-type { width:100%; font-size:.24rem; color:#585858; padding:.02rem 0;}
.ddxq-danhao p:last-of-type { width:100%; font-size:.2rem; color:#999999; padding:.02rem 0;}
.csgl-ddxq-contk i { display:block; box-sizing:border-box; width:20%; float:right; font-size:.48rem; color:#d6d6d6; text-align:right; padding:.04rem 0;}
.csgl-ddxq-contk::after { content:""; display:block; clear:both;}
.ddxq-wlgz { display:inline-block; color:#ef3430; text-indent:.1rem;}
.csgl-ddxq-cont .csgl-ddxq-contk .ddxq-blue { color:#00dbf5;}

.csgl-wlgz-gaikuo { width:100%; box-sizing:border-box; padding:.2rem 0; background-color:#fff;}
.csgl-wlgz-gaikuo ul { width:90%; margin:0 auto;}
.csgl-wlgz-gaikuo ul li:first-of-type { width:25%; height:1.2rem; box-sizing:border-box; float:left; text-align:center; vertical-align:middle; overflow:hidden; position:relative;}
.csgl-wlgz-gaikuo ul li:first-of-type p { width:100%; height:.4rem; line-height:.4rem; background-color:rgba(0,0,0,.45); position:absolute; z-index:9; bottom:0; color:#fff; font-size:.2rem;}
.csgl-wlgz-gaikuo ul li:first-of-type img { width:100%; height:100%; margin:0 auto; position:absolute; z-index:1;}
.csgl-wlgz-gaikuo ul li:last-of-type { width:75%; height:1.2rem; box-sizing:border-box; float:left; text-indent:.2rem;}
.csgl-wlgz-gaikuo ul li:last-of-type p { width:100%; height:.3rem; line-height:.3rem; font-size:.2rem;}
.wlgz-blue {color:#00dbf5;}
.wlgz-juhong {color:#ef3430;}
.csgl-wlgz-gaikuo ul::after { content:""; display:block; clear:both;}
.csgl-wlgz-cont { width:100%; background-color:#fff; margin-top:.16rem;}
.wlgz-cont-bt { width:90%; box-sizing:border-box; margin:0 auto; padding:.2rem 0; color:#00dbf5; font-size:.24rem;}
.wlgz-cont-xq { width:90%; margin:0 auto; box-sizing:border-box; padding-left:.3rem;}
.wlgz-cont-xq ul li {width:100%; box-sizing:border-box; padding:.1rem 0; border-bottom:.02rem solid #f0f0f0;}
.wlgz-cont-xq ul li p:first-of-type { width:100%; font-size:.2rem; color:#585858; padding:.02rem 0;}
.wlgz-cont-xq ul li p:last-of-type { width:100%; font-size:.16rem; color:#999999; padding:.02rem 0;}

.sear-wk .ddtj-cxxz { width:24%; float:right;}
.ddtj-cxxz .ddtj-sx { width:50%; height:.6rem; line-height:.66rem; float:left; text-align:center;}
.ddtj-cxxz .ddtj-sx i {font-size:.38rem; color:#00dbf5;}
.ddtj-cxxz .ddtj-download { width:50%; height:.6rem; line-height:.66rem; float:right; text-align:right;}
.ddtj-cxxz .ddtj-download i {font-size:.42rem; color:#00dbf5;}
.ddtj-cxxz::after { content:""; display:block; clear:both;}

.ddtj-sx-modal {width: 80%; position: absolute; z-index: 9999; left: 50%; margin-left: -40%; margin-top: 0; top: 50%; text-align: center; border-radius: 0.06rem; opacity: 1; -webkit-transition-duration: 400ms; transition-duration: 400ms; -webkit-transform: translate3d(0, 0, 0) scale(1); transform: translate3d(0, 0, 0) scale(1); color: #585858; display:none; margin-top: -35%;}
.ddtj-sx-modal-inner { padding: 0.2rem; border-radius: 0.1rem 0.1rem 0 0; position: relative; background: #fff;}
.ddtj-sx-modal-inner:after { content: ''; position: absolute; left: 0; bottom: 0; right: auto; top: auto; height: .02rem; width: 100%; background-color: #f0f0f0; display: block; z-index: 15; -webkit-transform-origin: 50% 100%; transform-origin: 50% 100%;}
.ddtj-sx-modal-inner .order-sx-modal-text { width:100%; height:.5rem; text-align:left; line-height:.5rem; border-bottom:.02rem solid #f0f0f0; font-size:.24rem;}
.ddtj-sx-modal-inner form { width:100%; height:.5rem; margin-top:.2rem;}
.ddtj-sx-modal-inner form label { display:inline-block; width:25%; height:.5rem; line-height:.5rem; float:left; font-size:.2rem;}
.ddtj-sx-modal-inner form select { display:inline-block; width:75%; height:.5rem; line-height:.5rem; float:right; box-sizing:border-box; font-size:.2rem; outline:none;}
.ddtj-sx-modal-inner form select option {display:inline-block; width:75%; height:.5rem; line-height:.5rem;  font-size:.2rem;}
.ddtj-sx-modal-inner form input {display:inline-block; width:75%; height:.5rem; line-height:.5rem; float:right; box-sizing:border-box; font-size:.2rem; outline:none;}

.ddtj-sx-modal-buttons { height:.7rem; overflow: hidden; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: center; -webkit-justify-content: center; justify-content: center;}
.ddtj-sx-modal-buttons span:first-of-type::after {content: ''; position: absolute; right: 0; top: 0; left: auto; bottom: auto; width: .02rem; height: 100%; background-color: #f0f0f0; display: block; z-index: 15; -webkit-transform-origin: 100% 50%; transform-origin: 100% 50%;}
.ddtj-sx-modal-button:first-child { border-radius: 0 0 0 0.1rem;}
.ddtj-sx-modal-button { width: 100%; padding: 0 0.25rem; height: .7rem; font-size: 0.24rem; line-height: .7rem; text-align: center; color: #42e0f6; background: #fff; display: block; position: relative; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; cursor: pointer; box-sizing: border-box; -webkit-box-flex: 1; -ms-flex: 1; letter-spacing:.1rem;}
.ddtj-sx-modal-button:last-child {border-radius: 0 0 .1rem 0;}

.bbxt-ddtj-cont { width:96%; margin:0 auto; margin-top:.16rem; margin-bottom:.94rem;}
.bbxt-ddtj-cont-wk { width:100%; box-sizing:border-box; background-color:#fff; border-radius:0.1rem; -webkit-border-radius:0.1rem; -moz-border-radius:0.1rem; padding-top:.2rem; padding-left:.2rem; padding-right:.2rem; font-size:.2rem; color:#999999; margin-bottom: .1rem;}
.bbxt-ddtj-cont-wk .bbxt-ddtj-wz p:first-of-type {width:50%; box-sizing:border-box; float:left; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.bbxt-ddtj-cont-wk .bbxt-ddtj-wz p:last-of-type {width:50%; box-sizing:border-box; float:right; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.bbxt-ddtj-cont-wk li:first-of-type { border-bottom:.02rem solid #f0f0f0; padding-bottom:.1rem;}
.bbxt-ddtj-wz { width:100%; height:.3rem; line-height:.3rem;}
.bbxt-ddtj-wz::after { content:""; display:block; clear:both;}
.bbxt-ddtj-color1 {color:#585858; font-size:.22rem;}
.bbxt-ddtj-color2 {color:#42e0f6; font-size:.22rem;}
.bbxt-ddtj-color3 {color:#42e0f6;}
.bbxt-ddtj-color4 {color:#f74343;}
.bbxt-ddtj-xhx {color:#ff6339;}
.bbxt-ddtj-cont-wk li:last-of-type { width:100%; padding-top:.14rem; padding-bottom:.1rem;}
.bbxt-ddtj-cont-wk li:last-of-type p:first-of-type {width:50%; height:.5rem; line-height:.5rem; float:left; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.bbxt-ddtj-cont-wk li:last-of-type::after { content:""; display:block; clear:both;}




