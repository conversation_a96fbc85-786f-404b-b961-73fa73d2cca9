/*-------------------------------------
zTree Style

version:	2.0
author:		Hunter.z
email:		<EMAIL>
website:	http://code.google.com/p/jquerytree/

-------------------------------------*/

.tree{
	margin:0; 
	padding: 5px;
	color:#333;
	font-size:12px;
	font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif;
	}

.tree li{
	padding:0; margin:0; 
	list-style:none;
	line-height:18px; 
	text-align:left; 
	white-space:nowrap;
	}

.tree li ul{ margin:0; padding:0 0 0 18px;}
.tree li ul.line{ background:url(./img/line_conn.gif) 0 0 repeat-y;}

.tree li a {
	padding:0; margin:0 10px 0 0; 
	cursor:pointer; 
	color:#000000; 
	text-decoration:none;
	}
.tree li a:hover {text-decoration:underline;}
.tree li a.curSelectedNode {
	background-color:#FFE6B0; 
	color:black;
	border:1px #FFB951 solid; 
	opacity:0.8; filter:alpha(opacity=80);
	}
.tree li a.curSelectedNode_Edit {
	line-height: 18px;
	background-color:#FFE6B0; 
	color:black;
	border:1px #FFB951 solid; 
	opacity:0.8; filter:alpha(opacity=80);
	}
.tree li a.tmpTargetNode {
	background-color:#316AC5; 
	color:white;
	border:1px #316AC5 solid; 
	opacity:0.8; filter:alpha(opacity=80);
	}
.tree li a input.rename {height:13px; width:80px; padding:0; margin:0; font-size:12px;}	

.tree li button {
	width:18px; height:18px; 
	padding:0; margin:0; 
	vertical-align:middle;
	border:0 none; 
	background-color: transparent; 
	background-repeat: no-repeat;
	background-position: 0 0;
	cursor: pointer;
	}
.tree li button.ico {
	padding:0; margin:0 2px 0 0; 
	}
	
.tree li button.edit {background:url("./img/edit.png") no-repeat scroll 0 0 transparent;}	
.tree li button.remove {background:url("./img/remove.png") no-repeat scroll 0 0 transparent;}	

.tree li button.chk {
	width:13px; height:13px; 
	padding:0; margin:0 3px 0 0; 
	vertical-align:middle;
	border:0 none;
	cursor: auto;
	}
.tree li button.chk.checkbox_false_full { background:url("./img/checkbox.png") no-repeat scroll 0 0 transparent;}	
.tree li button.chk.checkbox_false_full_focus { background:url("./img/checkbox.png") no-repeat scroll 0 -12px transparent;}	
.tree li button.chk.checkbox_false_part { background:url("./img/checkbox.png") no-repeat scroll 0 -72px transparent;}	
.tree li button.chk.checkbox_false_part_focus { background:url("./img/checkbox.png") no-repeat scroll 0 -84px transparent;}	
.tree li button.chk.checkbox_true_full { background:url("./img/checkbox.png") no-repeat scroll 0 -24px transparent;}	
.tree li button.chk.checkbox_true_full_focus { background:url("./img/checkbox.png") no-repeat scroll 0 -36px transparent;}	
.tree li button.chk.checkbox_true_part { background:url("./img/checkbox.png") no-repeat scroll 0 -48px transparent;}	
.tree li button.chk.checkbox_true_part_focus { background:url("./img/checkbox.png") no-repeat scroll 0 -60px transparent;}	

.tree li button.chk.radio_false_full { background:url("./img/radio.png") no-repeat scroll 0 0 transparent;}	
.tree li button.chk.radio_false_full_focus { background:url("./img/radio.png") no-repeat scroll 0 -12px transparent;}	
.tree li button.chk.radio_false_part { background:url("./img/radio.png") no-repeat scroll 0 -72px transparent;}	
.tree li button.chk.radio_false_part_focus { background:url("./img/radio.png") no-repeat scroll 0 -84px transparent;}	
.tree li button.chk.radio_true_full { background:url("./img/radio.png") no-repeat scroll 0 -24px transparent;}	
.tree li button.chk.radio_true_full_focus { background:url("./img/radio.png") no-repeat scroll 0 -36px transparent;}	
.tree li button.chk.radio_true_part { background:url("./img/radio.png") no-repeat scroll 0 -48px transparent;}	
.tree li button.chk.radio_true_part_focus { background:url("./img/radio.png") no-repeat scroll 0 -60px transparent;}	

.tree li button.switch_root_open{ background:url(./img/minus_root.png);}
.tree li button.switch_root_close{ background:url(./img/plus_root.png);}
.tree li button.switch_roots_open{ background:url(./img/minus_top.png);}
.tree li button.switch_roots_close{ background:url(./img/plus_top.png);}
.tree li button.switch_center_open{ background:url(./img/minus_center.png);}
.tree li button.switch_center_close{ background:url(./img/plus_center.png);}
.tree li button.switch_bottom_open{ background:url(./img/minus_bottom.png);}
.tree li button.switch_bottom_close{ background:url(./img/plus_bottom.png);}
.tree li button.switch_noLine_open{ background:url(./img/minus_noLine.png);}
.tree li button.switch_noLine_close{ background:url(./img/plus_noLine.png);}

.tree li button.switch_root_docu{ background:none;}
.tree li button.switch_roots_docu{ background:url(./img/line_top.gif);}
.tree li button.switch_center_docu{ background:url(./img/line_center.gif);}
.tree li button.switch_bottom_docu{ background:url(./img/line_bottom.gif);}
.tree li button.switch_noLine_docu{ background:none;}

.tree li button.ico_loading{ background:url(./img/loading.gif) no-repeat scroll 1px 1px transparent;}
.tree li button.ico_open{ background:url(./img/folder_Open.png);}
.tree li button.ico_close{ background:url(./img/folder_Close.png);}
.tree li button.ico_docu{ background:url(./img/page.png);}

.tree INPUT.checkbox {
	padding:0; margin:0 2px 0 0; 
	width:18px; height:18px; 
	vertical-align:middle; 
	}

.tmpTargetTree {
	background-color:#FFE6B0; 
	border:0px #FFB951 solid; 
	opacity:0.8; filter:alpha(opacity=80);
	}

button.tmpzTreeMove_arrow {
	width:16px; height:16px; 
	padding:0; margin:2px 0 0 1px; 
	border:0 none; 
	position:absolute; 
	background:url(./img/moveArrow.png) no-repeat scroll 0 0 transparent;
	}
	
.zTreeDragUL {
	margin:0; padding:0; 
	position:absolute; 
	background-color:#cfcfcf; 
	border:1px #00B83F dotted; 
	opacity:0.8; filter:alpha(opacity=80);
	}

.zTreeMask {
	 z-index:10000; 
	 background-color:#cfcfcf; 
	 opacity:0.0; filter:alpha(opacity=0); 
	 position:absolute;
}

