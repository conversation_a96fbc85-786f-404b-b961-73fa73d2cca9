
/*��������*/
.dialog_borderWidth{
	width:13px;
}
.dialog_title{
	padding: 5px 0 0 4px; 
	float: left; 
	font-weight: bold; 
	color:#fff;
}
.dialog_lt{
	background-image: url(popup/dialog_lt.png) !important;
	background: url(popup/dialog_lt.gif) no-repeat 0 0;
	height:25px;
}
.dialog_ct{
	background-image:url(popup/dialog_ct.png) !important;
	background: url(popup/dialog_ct.gif) repeat-x top;
	height:25px;
	vertical-align:top;
}
.dialog_rt{
	background-image: url(popup/dialog_rt.png) !important;
	background: url(popup/dialog_rt.gif) no-repeat right 0;
	height:25px;
}
.icon_dialog{
	background-image:url(popup/icon_dialog.png) !important;
	background-image:url(popup/icon_dialog.gif);
	border:0;
	padding-top:0px;
	padding-left:7px;
	padding-right:7px;
	padding-bottom:7px;
	background-repeat:no-repeat;
	background-color:transparent;
	width:14px;
	height:16px;
}
.dialog_closebtn{
	background-image:url(popup/dialog_closebtn.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	
	margin: 4px 0 0;
	*margin-top: 5px;
	 position: relative;
	 top:auto; 
	 cursor: pointer; 
	 float: right; 
	 height: 17px; 
	 width: 28px;
}
.dialog_closebtn_over{
	background-image:url(popup/dialog_closebtn_over.png);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	
	margin: 4px 0 0;
	*margin-top: 5px;
	 position: relative;
	 top:auto; 
	 cursor: pointer; 
	 float: right; 
	 height: 17px; 
	 width: 28px;
}
.dialog_maxbtn{
	background-image:url(popup/dialog_maxbtn.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	
	margin: 4px 0 0;
	*margin-top: 5px; 
	position: relative;
	top:auto; 
	cursor: pointer; 
	float: right; 
	height: 17px; 
	width: 28px; 
}
.dialog_minbtn{
	background-image:url(popup/dialog_minbtn.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	
	margin: 4px 0 0;
	*margin-top: 5px; 
	position: relative;
	top:auto; 
	cursor: pointer; 
	float: right; 
	height: 17px; 
	width: 28px; 
}

.dialog_decreasebtn{
	background-image:url(popup/dialog_decreasebtn.gif)!important;
}

.dialog_mlm{
	background-image: url(popup/dialog_mlm.png) !important;
	background: url(popup/dialog_mlm.gif) repeat-y left;
}
.dialog_bg{
	background:#eaece9 url(popup/dialog_bg.jpg) no-repeat scroll right top;
}
.dialog_messageIcon{
	background-image:url(popup/window.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	width:32px;
	height:32px;
}
/* Q Q 3 135 96 79 0*/
.dialog_mrm{
	background-image: url(popup/dialog_mrm.png) !important;
	background: url(popup/dialog_mrm.gif) repeat-y right;
}
.dialog_lb{
	background-image: url(popup/dialog_lb.png) !important;
	background: url(popup/dialog_lb.png) no-repeat 0 bottom;
	height:13px;
}
.dialog_cb{
	background-image: url(popup/dialog_cb.png) !important;
	background: url(popup/dialog_cb.gif) repeat-x bottom;
	height:13px;
}
.dialog_rb{
	background-image: url(popup/dialog_rb.png) !important;
	background: url(popup/dialog_rb.gif) no-repeat right bottom;
	height:13px;
}
.icon_alert{
	background-image:url(popup/icon_alert.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	width:34px;
	height:34px;
}
.icon_query{
	background-image:url(popup/icon_query.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	width:34px;
	height:34px;
}

.dialog_sample_top{
	border:solid 1px #999999;
	background-color:#eeeeee;
	height:32px;
	font-weight:bold;
	color:black;
}
.dialog__simple_closebtn{
	background-image:url(popup/simple_close.gif);
	border:0;
	background-repeat:no-repeat;
	background-color:transparent;
	background-position:50% 50%;
	width:10px;
	height:10px;
	margin: 10px 8px 0 0; 
	position: relative; 
	top: auto; 
	cursor: pointer; 
	float: right;
}
.dialog_sample_middle{
	border-left:solid 1px #999999;
	border-right:solid 1px #999999;
	border-bottom:solid 1px #999999;
	background-color:#ffffff;
}
.dialog_tip_middle{
	border-left:solid 1px #999999;
	border-right:solid 1px #999999;
	background-color:#ffffff;
}
.dialog_tip_bottom{
	height:31px;
	background-image:url(popup/simple_bottomLine.gif);
	background-repeat:repeat-x;;
}
.dialog_tip_bottomArr{
	height:100%;
	background-image: url(popup/simple_tip.png) !important;
	background: url(popup/simple_tip.gif) no-repeat center top;
}
.dialog_shadow_lt{
	background-image:url(popup/shadow_lt.gif);
	background-repeat:no-repeat;
	background-color:#ffffff;
	height:39px;
	width:220px;
}
.dialog_shadow_ct{
	background-image:url(popup/shadow_ct.gif);
	background-repeat: repeat-x;
	background-color:#ffffff;
	height:39px;
}
.dialog_shadow_rt{
	background-image:url(popup/shadow_rt.gif);
	background-repeat:no-repeat;
	height:39px;
	width:225px;
}
.dialog_shadow_lm{
	background-image:url(popup/shadow_lm.gif);
	background-repeat: repeat-y;
	background-color:#ffffff;
}
.dialog_shadow_cm{
	background-color:#ffffff;
}
.dialog_shadow_rm{
	background-image:url(popup/shadow_rm.gif);
	background-repeat: repeat-y;
}
.dialog_shadow_lb{
	background-image: url(popup/shadow_lb.png) !important;
	background-image:url(popup/shadow_lb.gif);
	background-repeat:no-repeat;
	height:130px;
	width:220px;
}
.dialog_shadow_cb{
	background-image: url(popup/shadow_cb.png) !important;
	background-image:url(popup/shadow_cb.gif);
	background-repeat: repeat-x;
	background-position:0 100%;
	height:130px;
}
.dialog_shadow_rb{
	background-image: url(popup/shadow_rb.png) !important;
	background-image:url(popup/shadow_rb.gif);
	background-repeat:no-repeat;
	height:130px;
	width:225px;
}
.dialog_shadow_content{
	position:absolute;
	z-index:100;
	padding:0 0 0 4px;
}
.dialog_shadow_content_top{
	height:39px;
	font-weight:bold;
	color:black;
}
/*�������� QQ 3 135 9679 0*/
