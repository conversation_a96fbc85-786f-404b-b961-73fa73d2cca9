FROM frolvlad/alpine-java
VOLUME /tmp
ADD te-fras.jar app.jar
RUN sh -c 'touch /app.jar'
# 基于15.38GB内存优化，保守分配3GB给JVM
ENV JAVA_OPTS="-Xms8192M -Xmx8192M -Xmn2560M -Xss1M -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m -XX:SurvivorRatio=8 -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=32m -XX:+UseStringDeduplication -XX:G1MixedGCCountTarget=8 -XX:G1HeapWastePercent=5 -XX:+PrintGC -XX:+PrintGCTimeStamps"
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Duser.timezone=Asia/Shanghai -Djava.security.egd=file:/dev/./urandom -Drun.profiles=test  -jar /app.jar" ]